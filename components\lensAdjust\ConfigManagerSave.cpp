#include "ConfigManager.h"
#include <QSettings>
#include <QXmlStreamWriter>
#include <QFile>

// 保存系统配置
bool ConfigManager::saveSystemConfig()
{
    QString filePath = getConfigFilePath(SystemConfig);
    logInfo("Saving system configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入系统配置
    settings.beginGroup("SENSOR_BOARD");
    settings.setValue("version", m_systemConfig.version);
    settings.endGroup();
    
    settings.beginGroup("MES");
    settings.setValue("userid", m_systemConfig.userid);
    settings.setValue("op", m_systemConfig.op);
    settings.setValue("work_number", m_systemConfig.work_number);
    settings.setValue("work_domain", m_systemConfig.work_domain);
    settings.endGroup();
    
    settings.beginGroup("DEVICE");
    settings.setValue("sensor_device", m_systemConfig.sensor_device);
    settings.setValue("sensor_device_baud", m_systemConfig.sensor_device_baud);
    settings.setValue("station_number", m_systemConfig.station_number);
    settings.setValue("clens_machine_brand", m_systemConfig.clens_machine_brand);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to save system configuration");
        return false;
    }
    
    logInfo("System configuration saved successfully");
    return true;
}

// 保存光斑配置
bool ConfigManager::saveFaculaConfig()
{
    QString filePath = getConfigFilePath(FaculaConfig);
    logInfo("Saving facula configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    settings.setValue("facula_center_channels", m_faculaConfig.facula_center_channels);
    settings.setValue("facula_center_peak_threshold", m_faculaConfig.facula_center_peak_threshold);
    settings.setValue("facula_center_loc_x", m_faculaConfig.facula_center_loc_x);
    settings.setValue("facula_center_loc_y", m_faculaConfig.facula_center_loc_y);
    settings.endGroup();
    
    // 写入调节参数
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", m_faculaConfig.facula_ok_times);
    settings.setValue("solid_time", m_faculaConfig.solid_time);
    settings.setValue("facula_ng_handle", m_faculaConfig.facula_ng_handle);
    settings.endGroup();
    
    // 写入处理类型
    settings.beginGroup("FACULA_HANDLE");
    settings.setValue("facula_handle_type", m_faculaConfig.facula_handle_type);
    settings.endGroup();
    
    // 写入图像处理参数
    settings.beginGroup("FACULA_PROCESSING");
    settings.setValue("interpolation_type", m_faculaConfig.interpolation_type);
    settings.setValue("filter_types", m_faculaConfig.filter_types);
    settings.setValue("interpolation_offset", m_faculaConfig.interpolation_offset);
    settings.setValue("kalman_strength", m_faculaConfig.kalman_strength);
    settings.setValue("convolution_kernel_size", m_faculaConfig.convolution_kernel_size);
    settings.setValue("convolution_preset", m_faculaConfig.convolution_preset);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to save facula configuration");
        return false;
    }
    
    logInfo("Facula configuration saved successfully");
    return true;
}

// 保存硬件配置
bool ConfigManager::saveHardwareConfig()
{
    QString filePath = getConfigFilePath(HardwareConfig);
    logInfo("Saving hardware configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入硬件配置
    settings.beginGroup("HARDWARE");
    settings.setValue("xy_radius_limit", m_hardwareConfig.xy_radius_limit);
    settings.setValue("z_radius_limit", m_hardwareConfig.z_radius_limit);
    settings.setValue("x_step_dist", m_hardwareConfig.x_step_dist);
    settings.setValue("y_step_dist", m_hardwareConfig.y_step_dist);
    settings.setValue("z_step_dist", m_hardwareConfig.z_step_dist);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to save hardware configuration");
        return false;
    }
    
    logInfo("Hardware configuration saved successfully");
    return true;
}

// 保存算法配置
bool ConfigManager::saveAlgorithmConfig()
{
    QString filePath = getConfigFilePath(AlgorithmConfig);
    logInfo("Saving algorithm configuration: " + filePath);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        logError("Cannot open algorithm config file for writing: " + filePath);
        return false;
    }
    
    QXmlStreamWriter xmlWriter(&file);
    xmlWriter.setAutoFormatting(true);
    xmlWriter.writeStartDocument();
    
    xmlWriter.writeStartElement("Parameters");
    
    // 写入所有算法参数
    for (auto it = m_algorithmConfig.parameters.begin(); it != m_algorithmConfig.parameters.end(); ++it) {
        xmlWriter.writeTextElement(it.key(), QString::number(it.value()));
    }
    
    xmlWriter.writeEndElement(); // Parameters
    xmlWriter.writeEndDocument();
    
    file.close();
    
    logInfo("Algorithm configuration saved successfully");
    return true;
}

// 设置配置数据的方法
void ConfigManager::setSystemConfig(const SystemConfigData& config)
{
    m_systemConfig = config;
    emit configChanged(SystemConfig);
}

void ConfigManager::setFaculaConfig(const FaculaConfigData& config)
{
    m_faculaConfig = config;
    emit configChanged(FaculaConfig);
}

void ConfigManager::setHardwareConfig(const HardwareConfigData& config)
{
    m_hardwareConfig = config;
    emit configChanged(HardwareConfig);
}

void ConfigManager::setAlgorithmConfig(const AlgorithmConfigData& config)
{
    m_algorithmConfig = config;
    emit configChanged(AlgorithmConfig);
}

// 配置验证方法
bool ConfigManager::validateConfigs()
{
    logInfo("Validating all configurations...");
    
    bool isValid = true;
    
    // 验证系统配置
    if (m_systemConfig.version.isEmpty()) {
        logError("System config validation failed: version is empty");
        isValid = false;
    }
    
    if (m_systemConfig.sensor_device < 1 || m_systemConfig.sensor_device > 4) {
        logError("System config validation failed: invalid sensor_device");
        isValid = false;
    }
    
    // 验证光斑配置
    if (m_faculaConfig.facula_center_points.isEmpty()) {
        logError("Facula config validation failed: no center points defined");
        isValid = false;
    }
    
    if (m_faculaConfig.facula_center_peak_threshold == 0) {
        logError("Facula config validation failed: peak threshold is zero");
        isValid = false;
    }
    
    // 验证硬件配置
    if (m_hardwareConfig.xy_radius_limit == 0 || m_hardwareConfig.z_radius_limit == 0) {
        logError("Hardware config validation failed: invalid radius limits");
        isValid = false;
    }
    
    if (isValid) {
        logInfo("All configurations validated successfully");
    } else {
        logError("Configuration validation failed");
    }
    
    return isValid;
}

// 迁移旧配置文件
bool ConfigManager::migrateOldConfigs()
{
    logInfo("Migrating old configuration files...");
    
    // 检查是否存在旧的配置文件
    QString oldConfigPath = QApplication::applicationDirPath() + "/config/clen_config.ini";
    if (!QFile::exists(oldConfigPath)) {
        logInfo("No old configuration file found, skipping migration");
        return true;
    }
    
    // 读取旧配置文件
    QSettings oldSettings(oldConfigPath, QSettings::IniFormat);
    
    // 迁移到新的配置结构
    // 系统配置迁移
    m_systemConfig.version = oldSettings.value("SENSOR_BOARD/version", "1.0.0").toString();
    m_systemConfig.userid = oldSettings.value("MES/userid", "admin").toString();
    m_systemConfig.op = oldSettings.value("MES/op", "105").toString();
    m_systemConfig.work_number = oldSettings.value("MES/work_number", "10001").toString();
    m_systemConfig.work_domain = oldSettings.value("MES/work_domain", "001").toString();
    m_systemConfig.sensor_device = oldSettings.value("DEVICE/sensor_device", 4).toUInt();
    m_systemConfig.sensor_device_baud = oldSettings.value("DEVICE/sensor_device_baud", 230400).toUInt();
    m_systemConfig.station_number = oldSettings.value("DEVICE/station_number", 1).toUInt();
    m_systemConfig.clens_machine_brand = oldSettings.value("DEVICE/clens_machine_brand", 3).toUInt();
    
    // 光斑配置迁移
    m_faculaConfig.facula_center_loc_x = oldSettings.value("ADJUST_PARAM/facula_center_loc_x", 2).toUInt();
    m_faculaConfig.facula_center_loc_y = oldSettings.value("ADJUST_PARAM/facula_center_loc_y", 2).toUInt();
    m_faculaConfig.facula_center_channels = QString("%1,%2").arg(m_faculaConfig.facula_center_loc_x).arg(m_faculaConfig.facula_center_loc_y);
    m_faculaConfig.facula_center_points.append(QPoint(m_faculaConfig.facula_center_loc_x, m_faculaConfig.facula_center_loc_y));
    m_faculaConfig.facula_center_peak_threshold = 800; // 默认值
    m_faculaConfig.facula_ok_times = oldSettings.value("ADJUST_PARAM/facula_ok_time", 3).toUInt();
    m_faculaConfig.solid_time = oldSettings.value("ADJUST_PARAM/solid_time", 0).toUInt();
    m_faculaConfig.facula_ng_handle = oldSettings.value("ADJUST_PARAM/facula_ng_handle", 1).toUInt();
    m_faculaConfig.facula_handle_type = oldSettings.value("FACULA_HANDLE/facula_handle_type", 1).toUInt();
    
    // 保存迁移后的配置
    bool success = saveAllConfigs();
    
    if (success) {
        logInfo("Configuration migration completed successfully");
    } else {
        logError("Configuration migration failed");
    }
    
    return success;
}
