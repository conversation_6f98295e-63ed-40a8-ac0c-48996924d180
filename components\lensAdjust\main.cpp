#include "ConfigInitializer.h"
#include "ConfigManager.h"
#include "clensadjust.h"


#include <QApplication>
#include <QMessageBox>
#include <QStringList>


#include "qLog.h"


int main(int argc, char *argv[]) {
    QApplication a(argc, argv);

    // 初始化日志系统
    MyLogger::QLog::init();
    MyLogger::QLog::installMessageHandler();

    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust application starting...");

    // 初始化配置系统
    ConfigInitializer configInitializer;
    if (!configInitializer.initializeConfigs()) {
        QMessageBox::critical(nullptr, "Configuration Error", "Failed to initialize configuration files. Application will exit.");
        LOG_ERROR(MyLogger::LogType::INIT, "Configuration initialization failed");
        return -1;
    }

    LOG_INFO(MyLogger::LogType::INIT, "Configuration system initialized successfully");

    // 创建主窗口
    cLensAdjust w;

    LOG_INFO(MyLogger::LogType::INIT, "lenAdjust started successfully");

    w.show();
    return a.exec();
}
