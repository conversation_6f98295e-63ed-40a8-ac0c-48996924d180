#ifndef CONFIG_MANAGER_H
#define CONFIG_MANAGER_H

#include <QObject>
#include <QSettings>
#include <QMap>
#include <QString>
#include <QVariant>
#include <QPoint>
#include <QVector>
#include <QMutex>
#include <QFileSystemWatcher>

/**
 * @brief 统一的配置管理器类
 * 
 * 负责管理所有配置文件的加载、保存、验证和自动生成
 * 支持配置文件热重载和向后兼容性
 */
class ConfigManager : public QObject
{
    Q_OBJECT

public:
    // 配置文件类型枚举
    enum ConfigType {
        SystemConfig,    // 系统级配置
        FaculaConfig,    // 光斑调节配置
        HardwareConfig,  // 硬件配置
        AlgorithmConfig  // 算法参数配置
    };

    // 系统配置结构
    struct SystemConfigData {
        QString version;                // 版本信息
        QString userid;                 // 用户ID
        QString op;                     // 工序号
        QString work_number;            // 工单号
        QString work_domain;            // 工作域
        uint8_t sensor_device;          // 传感器设备
        uint32_t sensor_device_baud;    // 设备波特率
        uint8_t station_number;         // 工位号
        uint8_t clens_machine_brand;    // 镜片调节设备品牌
    };

    // 光斑配置结构
    struct FaculaConfigData {
        // 多通道光斑中心配置
        QString facula_center_channels;           // 多通道配置字符串
        QVector<QPoint> facula_center_points;     // 解析后的通道坐标列表
        uint32_t facula_center_peak_threshold;    // 多通道模式下的peak阈值
        
        // 兼容性配置
        uint8_t facula_center_loc_x;              // 单点X坐标
        uint8_t facula_center_loc_y;              // 单点Y坐标
        
        // 调节参数
        uint8_t facula_ok_times;                  // 判定次数
        uint32_t solid_time;                      // 固化时间
        uint8_t facula_ng_handle;                 // 异常处理方式
        uint8_t facula_handle_type;               // 处理类型
        
        // 图像处理参数
        uint8_t interpolation_type;               // 插值类型
        QString filter_types;                     // 滤波器类型列表
        float interpolation_offset;               // 插值偏移量
        float kalman_strength;                    // 卡尔曼滤波强度
        uint8_t convolution_kernel_size;          // 卷积核大小
        QString convolution_preset;               // 卷积预设
        // ... 其他图像处理参数
    };

    // 硬件配置结构
    struct HardwareConfigData {
        uint32_t xy_radius_limit;                 // XY轴限位半径
        uint32_t z_radius_limit;                  // Z轴限位
        uint8_t x_step_dist;                      // X单脉冲移动距离
        uint8_t y_step_dist;                      // Y单脉冲移动距离
        uint8_t z_step_dist;                      // Z单脉冲移动距离
    };

    // 算法配置结构
    struct AlgorithmConfigData {
        QMap<QString, int> parameters;            // 算法参数映射
    };

public:
    static ConfigManager* getInstance();
    
    // 配置加载和保存
    bool loadAllConfigs();
    bool saveAllConfigs();
    bool loadConfig(ConfigType type);
    bool saveConfig(ConfigType type);
    
    // 配置数据访问
    const SystemConfigData& getSystemConfig() const { return m_systemConfig; }
    const FaculaConfigData& getFaculaConfig() const { return m_faculaConfig; }
    const HardwareConfigData& getHardwareConfig() const { return m_hardwareConfig; }
    const AlgorithmConfigData& getAlgorithmConfig() const { return m_algorithmConfig; }
    
    // 配置数据修改
    void setSystemConfig(const SystemConfigData& config);
    void setFaculaConfig(const FaculaConfigData& config);
    void setHardwareConfig(const HardwareConfigData& config);
    void setAlgorithmConfig(const AlgorithmConfigData& config);
    
    // 配置文件管理
    bool generateDefaultConfigs();
    bool validateConfigs();
    bool migrateOldConfigs();
    
    // 配置文件路径
    QString getConfigFilePath(ConfigType type) const;
    bool configFileExists(ConfigType type) const;
    
    // 多通道配置解析
    QVector<QPoint> parseFaculaCenterChannels(const QString& channelsStr);
    bool isValidChannelPoint(const QPoint& point, uint8_t maxX = 255, uint8_t maxY = 255);

signals:
    void configChanged(ConfigType type);
    void configError(const QString& error);

private slots:
    void onConfigFileChanged(const QString& path);

private:
    explicit ConfigManager(QObject* parent = nullptr);
    ~ConfigManager();
    
    static ConfigManager* s_instance;
    static QMutex s_mutex;
    
    // 配置数据
    SystemConfigData m_systemConfig;
    FaculaConfigData m_faculaConfig;
    HardwareConfigData m_hardwareConfig;
    AlgorithmConfigData m_algorithmConfig;
    
    // 文件监控
    QFileSystemWatcher* m_fileWatcher;
    
    // 内部方法
    bool loadSystemConfig();
    bool loadFaculaConfig();
    bool loadHardwareConfig();
    bool loadAlgorithmConfig();
    
    bool saveSystemConfig();
    bool saveFaculaConfig();
    bool saveHardwareConfig();
    bool saveAlgorithmConfig();
    
    bool generateDefaultSystemConfig();
    bool generateDefaultFaculaConfig();
    bool generateDefaultHardwareConfig();
    bool generateDefaultAlgorithmConfig();
    
    void setupFileWatcher();
    void logError(const QString& message);
    void logInfo(const QString& message);
};

#endif // CONFIG_MANAGER_H
