#include "ConfigManager.h"
#include <QApplication>
#include <QDir>
#include <QSettings>
#include <QXmlStreamWriter>
#include <QFile>
#include <QTextStream>

// 生成默认系统配置
bool ConfigManager::generateDefaultSystemConfig()
{
    QString filePath = getConfigFilePath(SystemConfig);
    logInfo("Generating default system configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入默认系统配置
    settings.beginGroup("SENSOR_BOARD");
    settings.setValue("version", "2.6.1.6");
    settings.endGroup();
    
    settings.beginGroup("MES");
    settings.setValue("userid", "admin");
    settings.setValue("op", "105");
    settings.setValue("work_number", "10001");
    settings.setValue("work_domain", "001");
    settings.endGroup();
    
    settings.beginGroup("DEVICE");
    settings.setValue("sensor_device", 4);
    settings.setValue("sensor_device_baud", 230400);
    settings.setValue("station_number", 1);
    settings.setValue("clens_machine_brand", 3);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to generate default system configuration");
        return false;
    }
    
    logInfo("Default system configuration generated successfully");
    return true;
}

// 生成默认光斑配置
bool ConfigManager::generateDefaultFaculaConfig()
{
    QString filePath = getConfigFilePath(FaculaConfig);
    logInfo("Generating default facula configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    settings.setValue("facula_center_channels", "2,2");
    settings.setValue("facula_center_peak_threshold", 800);
    settings.setValue("facula_center_loc_x", 2);
    settings.setValue("facula_center_loc_y", 2);
    settings.endGroup();
    
    // 写入调节参数
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", 3);
    settings.setValue("solid_time", 0);
    settings.setValue("facula_ng_handle", 1);
    settings.endGroup();
    
    // 写入处理类型
    settings.beginGroup("FACULA_HANDLE");
    settings.setValue("facula_handle_type", 1);
    settings.endGroup();
    
    // 写入图像处理参数
    settings.beginGroup("FACULA_PROCESSING");
    settings.setValue("interpolation_type", 0);
    settings.setValue("filter_types", "6");
    settings.setValue("interpolation_offset", 0.5);
    settings.setValue("kalman_strength", 1.0);
    settings.setValue("convolution_kernel_size", 3);
    settings.setValue("convolution_preset", "sharpen");
    settings.setValue("median_kernel_size", 3);
    settings.setValue("median_preset", "noise_reduction");
    settings.setValue("gaussian_sigma", 1.0);
    settings.setValue("gaussian_kernel_size", 5);
    settings.setValue("gaussian_preset", "medium_blur");
    settings.setValue("bilateral_sigma_color", 75.0);
    settings.setValue("bilateral_sigma_space", 75.0);
    settings.setValue("bilateral_kernel_size", 5);
    settings.setValue("bilateral_preset", "smooth");
    settings.setValue("weighted_avg_kernel_size", 3);
    settings.setValue("weighted_avg_preset", "center_weighted");
    settings.setValue("filter_strength", 1.0);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to generate default facula configuration");
        return false;
    }
    
    logInfo("Default facula configuration generated successfully");
    return true;
}

// 生成默认硬件配置
bool ConfigManager::generateDefaultHardwareConfig()
{
    QString filePath = getConfigFilePath(HardwareConfig);
    logInfo("Generating default hardware configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入硬件配置
    settings.beginGroup("HARDWARE");
    settings.setValue("xy_radius_limit", 1500);
    settings.setValue("z_radius_limit", 1400);
    settings.setValue("x_step_dist", 10);
    settings.setValue("y_step_dist", 10);
    settings.setValue("z_step_dist", 10);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        logError("Failed to generate default hardware configuration");
        return false;
    }
    
    logInfo("Default hardware configuration generated successfully");
    return true;
}

// 生成默认算法配置
bool ConfigManager::generateDefaultAlgorithmConfig()
{
    QString filePath = getConfigFilePath(AlgorithmConfig);
    logInfo("Generating default algorithm configuration: " + filePath);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        logError("Cannot create algorithm config file: " + filePath);
        return false;
    }
    
    QXmlStreamWriter xmlWriter(&file);
    xmlWriter.setAutoFormatting(true);
    xmlWriter.writeStartDocument();
    
    xmlWriter.writeStartElement("Parameters");
    
    // 调节参数
    xmlWriter.writeComment("调节参数");
    xmlWriter.writeTextElement("initial_x_dist", "0");
    xmlWriter.writeTextElement("initial_y_dist", "0");
    xmlWriter.writeTextElement("initial_z_dist", "0");
    xmlWriter.writeTextElement("find_origin_raduis", "140");
    xmlWriter.writeTextElement("find_angle_step", "20");
    xmlWriter.writeTextElement("find_radius_step", "140");
    xmlWriter.writeTextElement("find_times", "4");
    xmlWriter.writeTextElement("discard_pack_num", "1");
    xmlWriter.writeTextElement("default_z_direct", "1");
    xmlWriter.writeTextElement("z_move_step", "3");
    xmlWriter.writeTextElement("peak_ok_threshold", "550");
    
    // 对称调节参数
    xmlWriter.writeComment("对称调节参数");
    xmlWriter.writeTextElement("Amp_select", "30");
    xmlWriter.writeTextElement("ALR_mp_peak", "20");
    xmlWriter.writeTextElement("ALR_mp_peak_threshold", "100");
    xmlWriter.writeTextElement("AUD_mp_peak", "0");
    xmlWriter.writeTextElement("AUD_mp_peak_threshold", "100");
    xmlWriter.writeTextElement("Aedge_peak_threshold", "180");
    xmlWriter.writeTextElement("ACR_peak_delta", "120");
    xmlWriter.writeTextElement("ARR_peak_delta", "50");
    xmlWriter.writeTextElement("AMax_peak", "650");
    
    // 判定标准
    xmlWriter.writeComment("判定标准");
    xmlWriter.writeTextElement("edge_peak_threshold", "50");
    xmlWriter.writeTextElement("peak_threshold", "600");
    xmlWriter.writeTextElement("peak_max_threshold", "1000");
    xmlWriter.writeTextElement("CR_peak_delta", "150");
    
    // 功能测试参数
    xmlWriter.writeComment("功能测试参数");
    xmlWriter.writeTextElement("FT_LRmp_adjust_peak", "25");
    xmlWriter.writeTextElement("FT_LRmp_adjust_peak_threshold", "5");
    xmlWriter.writeTextElement("FT_UDmp_adjust_peak", "0");
    xmlWriter.writeTextElement("FT_UDmp_adjust_peak_threshold", "100");
    xmlWriter.writeTextElement("FT_LRmp_solid_peak", "25");
    xmlWriter.writeTextElement("FT_LRmp_solid_peak_threshold", "8");
    xmlWriter.writeTextElement("FT_UDmp_solid_peak", "0");
    xmlWriter.writeTextElement("FT_UDmp_solid_peak_threshold", "100");
    xmlWriter.writeTextElement("FT_LRmp_deflate_peak", "25");
    xmlWriter.writeTextElement("FT_LRmp_deflate_peak_threshold", "8");
    xmlWriter.writeTextElement("FT_UDmp_deflate_peak", "0");
    xmlWriter.writeTextElement("FT_UDmp_deflate_peak_threshold", "100");
    
    xmlWriter.writeEndElement(); // Parameters
    xmlWriter.writeEndDocument();
    
    file.close();
    
    logInfo("Default algorithm configuration generated successfully");
    return true;
}
