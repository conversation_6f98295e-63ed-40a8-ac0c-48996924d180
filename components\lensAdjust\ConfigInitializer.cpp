#include "ConfigInitializer.h"
#include "ConfigManager.h"
#include <QApplication>
#include <QDir>
#include <QFile>
#include <QDebug>
#include <QDateTime>
#include <QStandardPaths>

ConfigInitializer::ConfigInitializer(QObject* parent)
    : QObject(parent)
{
}

bool ConfigInitializer::initializeConfigs()
{
    logInfo("Starting configuration initialization...");
    emit initializationProgress("Starting configuration initialization...", 0);
    
    // 1. 创建配置目录
    if (!createConfigDirectory()) {
        emit initializationError("Failed to create configuration directory");
        return false;
    }
    emit initializationProgress("Configuration directory created", 20);
    
    // 2. 检查是否需要迁移旧配置
    if (!migrateFromOldConfigs()) {
        logWarning("Configuration migration failed, but continuing with defaults");
    }
    emit initializationProgress("Configuration migration completed", 40);
    
    // 3. 初始化配置管理器
    ConfigManager* configManager = ConfigManager::getInstance();
    if (!configManager->generateDefaultConfigs()) {
        emit initializationError("Failed to generate default configurations");
        return false;
    }
    emit initializationProgress("Default configurations generated", 60);
    
    // 4. 加载所有配置
    if (!configManager->loadAllConfigs()) {
        emit initializationError("Failed to load configurations");
        return false;
    }
    emit initializationProgress("Configurations loaded", 80);
    
    // 5. 验证配置完整性
    if (!configManager->validateConfigs()) {
        logWarning("Configuration validation failed, but continuing");
    }
    emit initializationProgress("Configuration validation completed", 90);
    
    // 6. 创建配置备份
    if (!backupConfigs()) {
        logWarning("Failed to create configuration backup");
    }
    emit initializationProgress("Configuration initialization completed", 100);
    
    logInfo("Configuration initialization completed successfully");
    emit initializationComplete();
    return true;
}

bool ConfigInitializer::checkConfigIntegrity()
{
    logInfo("Checking configuration file integrity...");
    
    ConfigManager* configManager = ConfigManager::getInstance();
    
    // 检查所有配置文件是否存在
    bool allFilesExist = true;
    allFilesExist &= configManager->configFileExists(ConfigManager::SystemConfig);
    allFilesExist &= configManager->configFileExists(ConfigManager::FaculaConfig);
    allFilesExist &= configManager->configFileExists(ConfigManager::HardwareConfig);
    allFilesExist &= configManager->configFileExists(ConfigManager::AlgorithmConfig);
    
    if (!allFilesExist) {
        logError("Some configuration files are missing");
        return false;
    }
    
    // 验证配置内容
    if (!configManager->validateConfigs()) {
        logError("Configuration validation failed");
        return false;
    }
    
    logInfo("Configuration integrity check passed");
    return true;
}

bool ConfigInitializer::migrateFromOldConfigs()
{
    logInfo("Checking for old configuration files to migrate...");
    
    QString configDir = getConfigDirectory();
    QString oldConfigPath = configDir + "/clen_config.ini";
    QString oldXmlPath = configDir + "/clen_config.xml";
    QString oldMachineXmlPath = configDir + "/clen_config_QH_SHJ.xml";
    
    // 检查是否存在旧配置文件
    bool hasOldConfigs = QFile::exists(oldConfigPath) || 
                        QFile::exists(oldXmlPath) || 
                        QFile::exists(oldMachineXmlPath);
    
    if (!hasOldConfigs) {
        logInfo("No old configuration files found, skipping migration");
        return true;
    }
    
    logInfo("Found old configuration files, starting migration...");
    
    // 创建备份目录
    QString backupDir = getBackupDirectory() + "/migration_" + 
                       QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QDir dir;
    if (!dir.mkpath(backupDir)) {
        logError("Failed to create migration backup directory: " + backupDir);
        return false;
    }
    
    // 备份旧配置文件
    if (QFile::exists(oldConfigPath)) {
        QFile::copy(oldConfigPath, backupDir + "/clen_config.ini");
        logInfo("Backed up old clen_config.ini");
    }
    
    if (QFile::exists(oldXmlPath)) {
        QFile::copy(oldXmlPath, backupDir + "/clen_config.xml");
        logInfo("Backed up old clen_config.xml");
    }
    
    if (QFile::exists(oldMachineXmlPath)) {
        QFile::copy(oldMachineXmlPath, backupDir + "/clen_config_QH_SHJ.xml");
        logInfo("Backed up old clen_config_QH_SHJ.xml");
    }
    
    // 执行迁移
    ConfigManager* configManager = ConfigManager::getInstance();
    if (!configManager->migrateOldConfigs()) {
        logError("Configuration migration failed");
        return false;
    }
    
    logInfo("Configuration migration completed successfully");
    return true;
}

bool ConfigInitializer::backupConfigs()
{
    logInfo("Creating configuration backup...");
    
    QString backupDir = getBackupDirectory() + "/backup_" + 
                       QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss");
    QDir dir;
    if (!dir.mkpath(backupDir)) {
        logError("Failed to create backup directory: " + backupDir);
        return false;
    }
    
    ConfigManager* configManager = ConfigManager::getInstance();
    
    // 备份所有配置文件
    QStringList configFiles = {
        configManager->getConfigFilePath(ConfigManager::SystemConfig),
        configManager->getConfigFilePath(ConfigManager::FaculaConfig),
        configManager->getConfigFilePath(ConfigManager::HardwareConfig),
        configManager->getConfigFilePath(ConfigManager::AlgorithmConfig)
    };
    
    for (const QString& configFile : configFiles) {
        if (QFile::exists(configFile)) {
            QFileInfo fileInfo(configFile);
            QString backupPath = backupDir + "/" + fileInfo.fileName();
            if (!QFile::copy(configFile, backupPath)) {
                logError("Failed to backup configuration file: " + configFile);
                return false;
            }
        }
    }
    
    logInfo("Configuration backup created successfully: " + backupDir);
    return true;
}

bool ConfigInitializer::restoreConfigs()
{
    logInfo("Restoring configuration from backup...");
    
    // 查找最新的备份目录
    QString backupBaseDir = getBackupDirectory();
    QDir dir(backupBaseDir);
    QStringList backupDirs = dir.entryList(QStringList() << "backup_*", QDir::Dirs, QDir::Name | QDir::Reversed);
    
    if (backupDirs.isEmpty()) {
        logError("No backup directories found");
        return false;
    }
    
    QString latestBackupDir = backupBaseDir + "/" + backupDirs.first();
    logInfo("Restoring from backup: " + latestBackupDir);
    
    // 恢复配置文件
    QDir backupDir(latestBackupDir);
    QStringList backupFiles = backupDir.entryList(QStringList() << "*.ini" << "*.xml", QDir::Files);
    
    QString configDir = getConfigDirectory();
    for (const QString& backupFile : backupFiles) {
        QString sourcePath = latestBackupDir + "/" + backupFile;
        QString targetPath = configDir + "/" + backupFile;
        
        // 删除现有文件
        if (QFile::exists(targetPath)) {
            QFile::remove(targetPath);
        }
        
        // 恢复备份文件
        if (!QFile::copy(sourcePath, targetPath)) {
            logError("Failed to restore configuration file: " + backupFile);
            return false;
        }
    }
    
    logInfo("Configuration restored successfully from backup");
    return true;
}

bool ConfigInitializer::createConfigDirectory()
{
    QString configDir = getConfigDirectory();
    QDir dir;
    
    if (!dir.exists(configDir)) {
        if (!dir.mkpath(configDir)) {
            logError("Failed to create configuration directory: " + configDir);
            return false;
        }
        logInfo("Created configuration directory: " + configDir);
    }
    
    // 创建备份目录
    QString backupDir = getBackupDirectory();
    if (!dir.exists(backupDir)) {
        if (!dir.mkpath(backupDir)) {
            logError("Failed to create backup directory: " + backupDir);
            return false;
        }
        logInfo("Created backup directory: " + backupDir);
    }
    
    return true;
}

QString ConfigInitializer::getConfigDirectory() const
{
    return QApplication::applicationDirPath() + "/config";
}

QString ConfigInitializer::getBackupDirectory() const
{
    return QApplication::applicationDirPath() + "/config/backup";
}

void ConfigInitializer::logInfo(const QString& message)
{
    qDebug() << "[ConfigInitializer]" << message;
}

void ConfigInitializer::logError(const QString& message)
{
    qCritical() << "[ConfigInitializer]" << message;
}

void ConfigInitializer::logWarning(const QString& message)
{
    qWarning() << "[ConfigInitializer]" << message;
}
