# 1.0
cmake_minimum_required(VERSION 3.5)

project(lensAdjust LANGUAGES CXX)

# 1.2 启用当前头文件目录？是什么目录？
set(CMAKE_INCLUDE_CURRENT_DIR ON) #包含.h文件？

# 1.3 QT配置
set(CMAKE_AUTOUIC ON) #ui
set(CMAKE_AUTOMOC ON) #moc
set(CMAKE_AUTORCC ON) #rcc编译开关 用于资源文件

# 1.4
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

############################################# 构建模式->输出配置 ################################

########################################### 编译 ###########################
# 2. 头文件和动态链接库
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}) #找头文件
LINK_DIRECTORIES(${PROJECT_SOURCE_DIR}/../lib) #找.so/.a库文件路径

set(QRC_FILE sub_resource.qrc)
qt5_add_resources(SUB_QRC ${QRC_FILE}) #用这个函数把这些QRC文件添加进来

#   读取 CHANGELOG.md 中的最新版本号
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_version_from_log.py ${CMAKE_CURRENT_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_VERSION
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

#   读取生成软件类型：测试版(build)，发布版(git commit)
execute_process(
    COMMAND ${Python3_EXECUTABLE} ${CMAKE_SOURCE_DIR}/scriptFile/get_sw_type.py ${CMAKE_SOURCE_DIR}
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE SW_TYPE
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

#   将项目+版本号存储在变量中
message(STATUS "${PROJECT_NAME} project version: ${SW_VERSION}")
set(PROJECT_VERSION "${PROGRAM_PREFIX}_${PROJECT_NAME}_V${SW_VERSION}")

#  设置窗口标题
add_definitions(-DLENS_ADJUST_APP_WINDOW_TITLE="${PROJECT_VERSION}")

# 设置目标名称
string(TIMESTAMP CURRENT_DATE "%Y%m%d") #   获取当前日期
# release-发布版 
# test-测试版, release build
# debug-调试版，debug build
string(STRIP "${SW_TYPE}" SW_TYPE)
if(SW_TYPE STREQUAL "release") 
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${BUILD_MODE}") #   使用新版本号命名生成的可执行文件
else()
set(TARGET_NAME "${PROGRAM_PREFIX}_${PROJECT_NAME}_v${SW_VERSION}_${CURRENT_DATE}_${SW_TYPE}") #   使用新版本号命名生成的可执行文件
endif()

# 输出可执行文件
# 1.变量配置
set(SOURCE_FILE
    main.cpp
    clenAdjustOperation.cpp
    clensadjust.cpp
    clensadjust.ui
    lensReadIni.cpp
    ConfigManager.cpp
    ConfigManagerGenerate.cpp
    ConfigManagerSave.cpp
    ConfigInitializer.cpp
    )
if(CMAKE_BUILD_TYPE IN_LIST RELEASE_BUILD_TYPES)
ADD_EXECUTABLE(${PROJECT_NAME} WIN32 ${SOURCE_FILE} ${SUB_QRC}) #logo.rc
else()
ADD_EXECUTABLE(${PROJECT_NAME} ${SOURCE_FILE} ${SUB_QRC}) #logo.rc
endif()

set_output_directories(${PROJECT_NAME})
set_target_properties(${PROJECT_NAME} PROPERTIES OUTPUT_NAME ${TARGET_NAME})
target_link_libraries(
    ${PROJECT_NAME} PRIVATE
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Sql
    Qt5::PrintSupport
    Qt5::Charts
    
    saveLoad_lib
    qLog_lib
    thread_lib
    processList_lib
    sensorLidar_lib
    photonSensor_lib
    communication_lib
    mySql_lib
    dataHandle_lib
    maxFind
    showModule_lib
    clensMachine
)

#   输出库
# 1.变量配置
set(SOURCE_FILE
    clenAdjustOperation.cpp
    clensadjust.cpp
    clensadjust.ui
    lensReadIni.cpp
    ConfigManager.cpp
    ConfigManagerGenerate.cpp
    ConfigManagerSave.cpp
    ConfigInitializer.cpp
    )
ADD_LIBRARY(${PROJECT_NAME}_lib STATIC ${SOURCE_FILE}) #

# 链接库文件
target_link_libraries(
    ${PROJECT_NAME}_lib PRIVATE
    Qt5::Core
    Qt5::Gui
    Qt5::Widgets
    Qt5::Sql
    Qt5::PrintSupport
    Qt5::Charts

    qLog_lib
    saveLoad_lib
    thread_lib
    sensorLidar_lib
    photonSensor_lib
    communication_lib
    mySql_lib
    dataHandle_lib
    maxFind
    showModule_lib
#    image_zoom
    clensMachine
    saveFile_lib
)


