#include "ConfigManager.h"
#include <QSettings>
#include <QXmlStreamWriter>
#include <QFile>

namespace Config {

// 保存系统配置
ConfigResult ConfigManager::saveSystemConfig()
{
    QString filePath = getConfigFilePath(FileType::System);
    logInfo("Saving system configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入系统配置
    settings.beginGroup("SENSOR_BOARD");
    settings.setValue("version", m_systemConfig.version);
    settings.endGroup();
    
    settings.beginGroup("MES");
    settings.setValue("userid", m_systemConfig.userid);
    settings.setValue("op", m_systemConfig.op);
    settings.setValue("work_number", m_systemConfig.work_number);
    settings.setValue("work_domain", m_systemConfig.work_domain);
    settings.endGroup();
    
    settings.beginGroup("DEVICE");
    settings.setValue("sensor_device", m_systemConfig.sensor_device);
    settings.setValue("sensor_device_baud", m_systemConfig.sensor_device_baud);
    settings.setValue("station_number", m_systemConfig.station_number);
    settings.setValue("clens_machine_brand", m_systemConfig.clens_machine_brand);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to save system configuration");
    }
    
    logInfo("System configuration saved successfully");
    return ConfigResult(true);
}

// 保存光斑配置
ConfigResult ConfigManager::saveFaculaConfig()
{
    QString filePath = getConfigFilePath(FileType::Facula);
    logInfo("Saving facula configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入光斑中心配置
    settings.beginGroup("FACULA_CENTER");
    settings.setValue("facula_center_channels", m_faculaConfig.facula_center_channels);
    settings.setValue("facula_center_peak_threshold", m_faculaConfig.facula_center_peak_threshold);
    settings.setValue("facula_center_loc_x", m_faculaConfig.facula_center_loc_x);
    settings.setValue("facula_center_loc_y", m_faculaConfig.facula_center_loc_y);
    settings.endGroup();
    
    // 写入调节参数
    settings.beginGroup("ADJUST_PARAM");
    settings.setValue("facula_ok_times", m_faculaConfig.facula_ok_times);
    settings.setValue("solid_time", m_faculaConfig.solid_time);
    settings.setValue("facula_ng_handle", m_faculaConfig.facula_ng_handle);
    settings.endGroup();
    
    // 写入处理类型
    settings.beginGroup("FACULA_HANDLE");
    settings.setValue("facula_handle_type", m_faculaConfig.facula_handle_type);
    settings.endGroup();
    
    // 写入图像处理参数
    settings.beginGroup("FACULA_PROCESSING");
    settings.setValue("interpolation_type", m_faculaConfig.interpolation_type);
    settings.setValue("filter_types", m_faculaConfig.filter_types);
    settings.setValue("interpolation_offset", m_faculaConfig.interpolation_offset);
    settings.setValue("kalman_strength", m_faculaConfig.kalman_strength);
    settings.setValue("convolution_kernel_size", m_faculaConfig.convolution_kernel_size);
    settings.setValue("convolution_preset", m_faculaConfig.convolution_preset);
    settings.setValue("median_kernel_size", m_faculaConfig.median_kernel_size);
    settings.setValue("median_preset", m_faculaConfig.median_preset);
    settings.setValue("gaussian_sigma", m_faculaConfig.gaussian_sigma);
    settings.setValue("gaussian_kernel_size", m_faculaConfig.gaussian_kernel_size);
    settings.setValue("gaussian_preset", m_faculaConfig.gaussian_preset);
    settings.setValue("bilateral_sigma_color", m_faculaConfig.bilateral_sigma_color);
    settings.setValue("bilateral_sigma_space", m_faculaConfig.bilateral_sigma_space);
    settings.setValue("bilateral_kernel_size", m_faculaConfig.bilateral_kernel_size);
    settings.setValue("bilateral_preset", m_faculaConfig.bilateral_preset);
    settings.setValue("weighted_avg_kernel_size", m_faculaConfig.weighted_avg_kernel_size);
    settings.setValue("weighted_avg_preset", m_faculaConfig.weighted_avg_preset);
    settings.setValue("filter_strength", m_faculaConfig.filter_strength);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to save facula configuration");
    }
    
    logInfo("Facula configuration saved successfully");
    return ConfigResult(true);
}

// 保存硬件配置
ConfigResult ConfigManager::saveHardwareConfig()
{
    QString filePath = getConfigFilePath(FileType::Hardware);
    logInfo("Saving hardware configuration: " + filePath);
    
    QSettings settings(filePath, QSettings::IniFormat);
    
    // 写入硬件配置
    settings.beginGroup("HARDWARE");
    settings.setValue("xy_radius_limit", m_hardwareConfig.xy_radius_limit);
    settings.setValue("z_radius_limit", m_hardwareConfig.z_radius_limit);
    settings.setValue("x_step_dist", m_hardwareConfig.x_step_dist);
    settings.setValue("y_step_dist", m_hardwareConfig.y_step_dist);
    settings.setValue("z_step_dist", m_hardwareConfig.z_step_dist);
    settings.endGroup();
    
    settings.sync();
    
    if (settings.status() != QSettings::NoError) {
        return ConfigResult(false, ErrorType::PermissionError, "Failed to save hardware configuration");
    }
    
    logInfo("Hardware configuration saved successfully");
    return ConfigResult(true);
}

// 保存算法配置
ConfigResult ConfigManager::saveAlgorithmConfig()
{
    QString filePath = getConfigFilePath(FileType::Algorithm);
    logInfo("Saving algorithm configuration: " + filePath);
    
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly | QIODevice::Text)) {
        return ConfigResult(false, ErrorType::PermissionError, "Cannot open algorithm config file for writing: " + filePath);
    }
    
    QXmlStreamWriter xmlWriter(&file);
    xmlWriter.setAutoFormatting(true);
    xmlWriter.writeStartDocument();
    
    xmlWriter.writeStartElement("Parameters");
    
    // 写入所有算法参数
    for (auto it = m_algorithmConfig.parameters.begin(); it != m_algorithmConfig.parameters.end(); ++it) {
        xmlWriter.writeTextElement(it.key(), QString::number(it.value()));
    }
    
    xmlWriter.writeEndElement(); // Parameters
    xmlWriter.writeEndDocument();
    
    file.close();
    
    logInfo("Algorithm configuration saved successfully");
    return ConfigResult(true);
}

} // namespace Config
