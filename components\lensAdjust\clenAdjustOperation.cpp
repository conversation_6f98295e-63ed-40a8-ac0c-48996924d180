#include "clenAdjustOperation.h"
#include <QSqlDatabase>
#include <math.h>
#include <qsqlquery.h>

#include "lenMachineFactory.h"

#include "qLog.h"
#include "saveExcel.h"
#include "typeConvert.h"
#include "uart.h"

// 在文件开头添加组件日志对象
// static const MyLogger::CComponentLog logger("CLenAdjustOpt");

#define CLEN_MACHINE_ID   1
#define MAP_ADJUST_INDEX  0  // 0-原map数据调整 1-优化后数据调整
#define MAIN_UART_BAUD    230400
#define DEVICE_UART_BAUD  115200


#define MES_SUPER_USER    "Uadmin"

#define BLOCK_LEN_MACHINE 0

CLenAdjustOpt::CLenAdjustOpt(const NClen::StUiConfig &config)
    : mi_load_(new CLoadXml),
      mst_config_(&config),
      m_timerId(0),
      m_port_update_cnt(0),
      mc_processList_(new TClen_process),
      mst_task_status_(new TClen_process::StStepRecord),
      mi_icomm_dev_(new CUART("", DEVICE_UART_BAUD)),
      mst_comm_status_(new StCommunicateStatus),
      m_message_box_(new QWidget),
      mi_save_file_(new CSaveExcel),
      mc_sql_handle_(new CLenSqlHandle),
      mst_work_order_(new CLenSqlHandle::StWorkOrder),
      mst_xpid_det_(new CLenSqlHandle::StXpiddet),
      mst_mes_xsub2_data_(new CLenSqlHandle::StResultToMes),
      mst_result_(new StResult),
      mst_dds_(new StDds),
      mst_product_detect_info_(new StProductDetectInfo),
      mst_move_distance_(new C3dHandMachine::St3D<int16_t>) {
    //********************************** varibles init ******************************
    //* 1.默认外部参数读取
    mst_iniConfig = DATACONFIG;
    LOG_INFO(MyLogger::LogType::INIT,
             QString("Config.ini - user id:%1, operator position:%2, sensor version:%3, work order:%4, sensor project:%5, len machine: %6, sensor project "
                     "baud:%7, ng handle: %8, facula handle type %9")
                 .arg(mst_iniConfig.userid)
                 .arg(mst_iniConfig.op)
                 .arg(mst_iniConfig.version)
                 .arg(mst_iniConfig.work_number)
                 .arg(mst_iniConfig.sensor_device)
                 .arg(mst_iniConfig.clens_machine_brand)
                 .arg(mst_iniConfig.sensor_device_buad)
                 .arg(mst_iniConfig.facula_ng_handle)
                 .arg(mst_iniConfig.facula_handle_type));

    mi_clen_machine_ =
        CLenMachineFactory::getInstance().lenMachineCreate((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand, mi_icomm_dev_, CLEN_MACHINE_ID);
    mi_icomm_ = new CUART("", mst_iniConfig.sensor_device_buad);  //默认配置

    mst_facula_sensor_info = ISensorBoardFactory::sst_facula_sensor_info[(ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device];

    // 设置兼容性的单一目标点
    mst_facula_sensor_info.target_tf.ax = mst_iniConfig.facula_center_loc_x;
    mst_facula_sensor_info.target_tf.ay = mst_iniConfig.facula_center_loc_y;

    mc_facula_context_ = new CFaculaContext(mst_facula_sensor_info);

    mi_top_board_ = ISensorBoardFactory::getInstance().sensorBoardCreate((ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device, mi_icomm_);  //设备端口

    //* greymap show update
    //    mst_map_info_->target_tf.ax = mst_iniConfig.facula_center_loc_x;
    //    mst_map_info_->target_tf.ay = mst_iniConfig.facula_center_loc_y;


    //* 正常以下参数放入 D4,T4配置文件
    //    IFaculaAdjust::greymapInfoUpdate(IPhotonSensor::e4302, mst_map_info_, mst_interpolation_map_info_);
    //    mst_map_info_->sensor_info = ISensorBoardFactory::sst_facula_sensor_info[(ISensorBoardFactory::ESensorBoard)mst_iniConfig.sensor_device];
    //    mi_top_board_ = new CSensorCoinD(mi_icomm_); //设备端口

    //    if(!IFaculaAdjust::targetFaculaArea(mst_facula_sensor_info)) {
    //        emit initErrorSignal("原光斑中心位置设置异常");
    //        //facula info error
    //    };
    //    if(!IFaculaAdjust::targetFaculaArea(mst_interpolation_map_info_)) {
    //        emit initErrorSignal("优化光斑中心位置配置异常");
    //    };
    //    emit mainMapSizeSignal(mst_map_info_->xlens, mst_map_info_->ylens);

    //    m_map_data_->map_matrix.resize(mst_map_info_->ylens); //
    //    m_map_interpolation_data_->map_matrix.resize(mst_interpolation_map_info_->ylens); //
    //    for (uint i = 0; i < mst_interpolation_map_info_->ylens; i++) {
    //        m_map_interpolation_data_->map_matrix[i].resize(mst_interpolation_map_info_->xlens);
    //    }

    //* 2.内部参数初始化
    m_solid_times = mst_iniConfig.solid_time / s_timer_ms;

    //**********************************************子线程***********************
    //* 1.初始化就创建子线程
    LOG_INFO(MyLogger::LogType::PROCESS_STATUS, QString("Main thread id:%1").arg(reinterpret_cast<quintptr>(QThread::currentThread())));
    m_sub_thread_ = new QThread;  // 创建线程对象

    /* 创建工作的类对象，千万不要指定给创建的对象指定父对象
     * 如果指定了: QObject::moveToThread: Cannot move objects with a parent
     */
    m_serial_thread_ = new CLensAdjustSerial(nullptr, mi_top_board_, mi_clen_machine_);
    m_serial_thread_->moveToThread(m_sub_thread_);  //将工作的类对象移动到创建的子线程对象中

    //* 1.3 启动线程
    m_sub_thread_->start();
    //    m_serial_thread_->task_id_change(4);


    //******************************************* connect ***************************
    qRegisterMetaType<CLenAdjustOpt::EProcessStep>("CLenAdjustOpt::EProcessStep");

    connect(this, &CLenAdjustOpt::subThreadSignal, m_serial_thread_, &CLensAdjustSerial::loop);
    connect(m_sub_thread_, &QThread::finished, m_serial_thread_, &QObject::deleteLater);


    connect(dynamic_cast<CSensorCoinD *>(mi_top_board_), &CSensorCoinD::dataOutput, this, &CLenAdjustOpt::sensorDataReceive);  //无法用多态实现
    void (CLenAdjustOpt::*ptrDataOutput1)(IClensMachine::ECommAck step, ECommStatus status, QByteArray bytes) = &CLenAdjustOpt::dataReceive;

    if ((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand == CLenMachineFactory::ELenMachine::eQING_HE) {
        connect(dynamic_cast<CClensMachineQH *>(mi_clen_machine_), &CClensMachineQH::dataOutput, this, ptrDataOutput1);  //&CLenAdjustOpt::dataReceive);
        void (CLenAdjustOpt::*ptrDataOutput2)(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff) = &CLenAdjustOpt::dataReceive;
        connect(dynamic_cast<CClensMachineQH *>(mi_clen_machine_), &CClensMachineQH::floatDataOutput, this, ptrDataOutput2);
    } else if ((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand == CLenMachineFactory::ELenMachine::eQING_HE_SHJ) {
        connect(dynamic_cast<CClensMachineQH_SHJ *>(mi_clen_machine_), &CClensMachineQH_SHJ::dataOutput, this, ptrDataOutput1);  //&CLenAdjustOpt::dataReceive);
        void (CLenAdjustOpt::*ptrDataOutput2)(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff) = &CLenAdjustOpt::dataReceive;
        connect(dynamic_cast<CClensMachineQH_SHJ *>(mi_clen_machine_), &CClensMachineQH_SHJ::floatDataOutput, this, ptrDataOutput2);
    } else {
        connect(dynamic_cast<CClensMachineST *>(mi_clen_machine_), &CClensMachineST::dataOutput, this, ptrDataOutput1);  //&CLenAdjustOpt::dataReceive);
        void (CLenAdjustOpt::*ptrDataOutput2)(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff) = &CLenAdjustOpt::dataReceive;
        connect(dynamic_cast<CClensMachineST *>(mi_clen_machine_), &CClensMachineST::floatDataOutput, this, ptrDataOutput2);
    }


    //************************************* task list init *************************
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::readyStep, &CLenAdjustOpt::readyAck, true, 200, 0, 0, 0));  // readyAck

    //
    if ((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand == CLenMachineFactory::ELenMachine::eQING_HE_SHJ) {
        mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::startStep, &CLenAdjustOpt::startAck, true, 0, 0, 100, 3));  //机台启动
        mv_task_list.append(
            mc_processList_->addCallbackFunction(&CLenAdjustOpt::lensCapped, &CLenAdjustOpt::lensCappedAck, true, 0, 0, 150, 20));  // 模组到位->模组串口插针
    } else {
        mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::startStep, &CLenAdjustOpt::startAck, true, 0, 0, 100, 3));  //机台启动
    }
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::calibMode, &CLenAdjustOpt::calibModeAck, true, 500, 0, 100, 5));           //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::chipIdStep, &CLenAdjustOpt::chipIdAck, true, 700, 0, 100, 5));             //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::sensorVersionStep, &CLenAdjustOpt::sensorVersionAck, true, 0, 0, 50, 3));  //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::greyMapMode, &CLenAdjustOpt::greyMapModeAck, true, 0, 0, 200, 3));
    //
    if ((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand == CLenMachineFactory::ELenMachine::eQING_HE_SHJ) {
        mv_task_list.append(mc_processList_->addCallbackFunction(
            &CLenAdjustOpt::lenMachineAutoAdjust, &CLenAdjustOpt::lenMachineAutoAdjustAck, true, 0, 0, 150, 20));  //粗调开始
        mv_task_list.append(mc_processList_->addCallbackFunction(
            &CLenAdjustOpt::lenMachineAdjustStatus, &CLenAdjustOpt::lenMachineAdjustStatusAck, true, 0, 0, 150, 20));  //等待粗调状态
    } else {
        mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::lensCapped, &CLenAdjustOpt::lensCappedAck, true, 0, 0, 150, 20));  //机台到位
    }

    mv_task_list.append(
        mc_processList_->addCallbackFunction(&CLenAdjustOpt::getOriginLoc, &CLenAdjustOpt::getOriginLocAck, true, 0, 0, 100, 3));  //获取初始桌标

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::dataStep, &CLenAdjustOpt::dataAck, true, 0, 0, 100, 5));  //
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::adjustStep, &CLenAdjustOpt::adjustAck, true, 0, 0, 100, 4));
    if ((CLenMachineFactory::ELenMachine)mst_iniConfig.clens_machine_brand == CLenMachineFactory::ELenMachine::eQING_HE_SHJ) {
        mv_task_list.append(
            mc_processList_->addCallbackFunction(&CLenAdjustOpt::getMoveStatusStep, &CLenAdjustOpt::getMoveStatusAck, true, 0, 0, 100, 4));  //获取运动状态
    }

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::getAdjustedLoc, &CLenAdjustOpt::getAdjustedLocAck, true, 0, 0, 100, 3));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::testStep, &CLenAdjustOpt::testAck, true, 0, 0, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::solidStep, &CLenAdjustOpt::solidAck, true, 0, 0, 100, 0));
    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::solidStatusStep, &CLenAdjustOpt::solidStatusAck, true, 0, 0, 200, 10));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::retestStep, &CLenAdjustOpt::retestAck, true, 0, 0, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::deflateStep, &CLenAdjustOpt::deflateACK, true, 0, 0, 100, 3));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::retest2Step, &CLenAdjustOpt::retest2Ack, true, 0, 0, 0, 0));

    mv_task_list.append(mc_processList_->addCallbackFunction(&CLenAdjustOpt::compStep, &CLenAdjustOpt::compAck, true, 0, 0, 100, 3));


    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    m_timerId = startTimer(s_timer_ms);

    //******************************** 3.数据库 ******************************
    //* local
    //* 3.数据库
    mm_result_data.clear();
    mm_result_data.insert("时间", "");  //无法排序
    mm_result_data.insert("芯片ID", "");
    mm_result_data.insert("标签号nbr", "");
    mm_result_data.insert("事务号", "");
    mm_result_data.insert("mes code", "");

    //    mm_result_data.insert("初始坐标x", "");
    //    mm_result_data.insert("初始坐标y", "");
    //    mm_result_data.insert("初始坐标z", "");
    mm_result_data.insert("光斑数据", "");
    mm_result_data.insert("光斑code", "");
    mm_result_data.insert("终止坐标x", "");
    mm_result_data.insert("终止坐标y", "");
    mm_result_data.insert("终止坐标z", "");
    mm_result_data.insert("固化光斑数据", "");
    mm_result_data.insert("固化光斑code", "");
    //    mm_result_data.insert("放气光斑数据", "");
    mm_result_data.insert("放气光斑code", "");
    mm_result_data.insert("光斑MP", "");
    mm_result_data.insert("运行时间", "");
    mm_result_data.insert("结果", "");
    mm_result_data.insert("fatal reason", "");

    QString desk_path = QApplication::applicationDirPath();  // QStandardPaths::writableLocation(QStandardPaths::DesktopLocation);
    mi_save_file_->createOneFile(desk_path, "/镜片装调", mm_result_data);

    //    float time_tmp = 10.0;
    //    mm_result_data["运行时间"] = QString::number(time_tmp, 'f', 1);
    //    mi_save_file_->writeFile(mm_result_data);


    //* cloud mes database
    if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES
        mst_mes_xsub2_data_->userid = mst_iniConfig.userid;
        mst_mes_xsub2_data_->op     = mst_iniConfig.op;
        mst_work_order_->wo_lot     = mst_iniConfig.work_number;
        mst_work_order_->domain     = mst_iniConfig.work_domain;
        //        mst_xpid_det_->work_order = mst_iniConfig.work_number;
    }
    if (!mc_sql_handle_->connectXSub2DetDB()) {
        QMessageBox::warning(m_message_box_, QString("数据库连接失败"), QString("请检查数据库连接"));
    }


    //*
    productTestInfoInit(mst_product_detect_info_);

    //#define MES_TEST
#ifdef MES_TEST
    //    mc_sql_handle_->connectTestDB();
    uint16_t error_dds = 0;

    mst_work_order_->wo_lot = "9028";
    mst_work_order_->domain = "001";
    //    error_dds = mc_sql_handle_->checkWorkOrder(mst_work_order_);

    for (;;) {
        error_dds = 0;
        //        if(error_dds == 0) {
        mst_xpid_det_->mcuid      = QString("360512504E56383538053199").toUpper();
        mst_xpid_det_->work_order = mst_work_order_->wo_lot;  // 6688
        mst_xpid_det_->domain     = mst_work_order_->domain;
        error_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, false);
        //        }
        //            qDebug() << "-i lenAdjust/ nbr: " << mst_mes_xsub2_data_->nbr << "error dds: " << error_dds;

        mc_sql_handle_->printfTagTable(*mst_xpid_det_);

        //        if(error_dds == 0) {
        //            mesDataHandle(mst_mes_xsub2_data_);
        //            error_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);
        //        }
    }


#endif
    //* 4.test
    //#define ALGORITHM_TEST
#ifdef ALGORITHM_TEST
    QByteArray tmp(100, 3);
    m_origin_bytes = tmp;
    //    m_origin_bytes.resize(100);
    mc_facula_context_->mergeDataClean();

    mst_comm_status_->comm_status = ECommStatus::eCOMM_COMP;
    //    mst_move_distance_->x = -24;
    //    mst_move_distance_->y = 98;
    //    mst_move_distance_->z = 0;
    //    mi_clen_machine_->deviceSpeedHandle(*mst_move_distance_);

    //    QVector<QVector<uint32_t>> matrix_tmp = {{56,23,15},{65,32,78},{12,45,62}}; //MP matrix ���据
    //    QVector<QVector<uint32_t>> matrix_interpolation_tmp(4, QVector<uint32_t>(4));
    //    m_my_interPolation_->bilinear_interpolation(matrix_tmp, matrix_interpolation_tmp);

#endif
}

CLenAdjustOpt::~CLenAdjustOpt() {
    delete mi_load_;

    delete mc_processList_;
    delete mst_task_status_;

    //* 子线程注销
    //    m_serial_thread_->m_task_id = 0;
    m_serial_thread_->task_id_change(0);
    m_sub_thread_->quit();
    m_sub_thread_->wait();
    delete m_sub_thread_;  //会析构 m_serial_thread_

    killTimer(m_timerId);
    QThread::msleep(50);


    delete mi_top_board_;
    delete mi_clen_machine_;

    if (mi_icomm_ != nullptr)
        delete mi_icomm_;
    if (mi_icomm_dev_ != nullptr)
        delete mi_icomm_dev_;

    delete mst_comm_status_;
    //    delete mst_merge_data_;
    /*释放内存*/
    //    uint8_t item_lens = mst_map_info_->table_item.length();
    //    if(item_lens != 0) {
    //        for(uint i = 0; i < item_lens; i++) {
    //            if(mst_map_info_->table_item.at(i) != nullptr)
    //                delete mst_map_info_->table_item.at(i);
    //        }
    //    }
    //    item_lens = mst_interpolation_map_info_->table_item.length();
    //    if(item_lens != 0) {
    //        for(uint i = 0; i < item_lens; i++) {
    //            if(mst_interpolation_map_info_->table_item.at(i) != nullptr)
    //                delete mst_interpolation_map_info_->table_item.at(i);
    //        }
    //    }
    //    delete mst_map_info_;
    //    delete mst_target_map_info_;
    //    delete m_map_data_;
    //    delete mst_interpolation_map_info_;
    //    delete m_map_interpolation_data_;

    //    delete m_my_interPolation_;
    if (mc_facula_context_ != nullptr)
        delete mc_facula_context_;
    delete mi_save_file_;
    delete mc_sql_handle_;
    delete mst_work_order_;
    delete mst_xpid_det_;
    delete mst_mes_xsub2_data_;
    delete m_message_box_;

    delete mst_result_;
    delete mst_dds_;
    delete mst_product_detect_info_;
    delete mst_move_distance_;
}


void CLenAdjustOpt::timerEvent(QTimerEvent *event) {
    //  Q_UNUSED(event);

#if 0
    //    for(;;) {
    //        error_dds = 0;
    //        if(error_dds == 0) {
    mst_xpid_det_->mcuid = QString("360512504E56383536128949").toUpper();
    mst_xpid_det_->work_order = "8148"; // mst_work_order_->wo_lot; //6688
    mst_xpid_det_->domin = "001"; //mst_work_order_->domin;
    mst_mes_xsub2_data_->nbr = "";
    uint16_t error_dds = mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, false);
    //        }

    LOG_DEBUG(MyLogger::LogType::PROCESS_STATUS, "NBR: %s, Error DDS: %d", mst_mes_xsub2_data_->nbr.toLocal8Bit().constData(), error_dds);
    //        if(error_dds == 0) {
    //            mesDataHandle(mst_mes_xsub2_data_);
    //            error_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);
    //        }
    //    }
#endif


    if (m_timerId == event->timerId()) {
        //*******************************非任务task
        //* 1.串口列表刷新
        m_port_update_cnt++;
        if (m_port_update_cnt == 200) {
            m_port_update_cnt = 0;
            portlistUpdate();
        }

        //******************************* 任务循环 ********************
        //        mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_);
        if (mc_processList_->tasksRun(this, &mv_task_list, mst_task_status_) == eFATAL) {  // tasks exec errror, dont have ack or other reason
            mst_dds_->process_step_dds |= (1 << (uint8_t)mst_task_status_->cur_step);
            emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_STEP_ERROR, mst_dds_->process_step_dds));

            mc_processList_->taskInit(&mv_task_list, mst_task_status_);  //
            mv_task_list[EProcessStep::eCOMPLETE].flag.stop = true;
        }

        //* 状态界面显示更新
        if (mst_task_status_->cur_step != mst_task_status_->last_step) {
            mst_task_status_->last_step = mst_task_status_->cur_step;

            QMetaEnum stepEnum = QMetaEnum::fromType<CLenAdjustOpt::EProcessStep>();
            QString   step_str = stepEnum.valueToKey(mst_task_status_->cur_step);

            //            if(mst_task_status_->cur_step == eMAP_DATA || )
            //            qDebug() << "-i lenAdjust/ step: " << step_str;

            //            if(mst_task_status_->cur_status != mst_task_status_->last_status) { //?
            //            emit stepStatusSignal((int16_t)mst_task_status_->cur_step, (int16_t)mst_task_status_->cur_ack_status);

            //            }
        }
    }
}

void CLenAdjustOpt::resultInit(StSubResult *result_) {
    result_->result = 0xff;  // true
    result_->mp_origin_data.clear();
    result_->map_matrix.clear();
}

/**
 * @brief synchronizate local data
 * @param product_info_
 */
void CLenAdjustOpt::productTestInfoInit(StProductDetectInfo *product_info_) {

    QString filename = QApplication::applicationDirPath() + "/config/clen_product_info.xml";
    mi_load_->readParam(filename, "clens_info", "product_detect_info", &m_product_info);

    product_info_->product_total_num = m_product_info["product_total_num"].toUInt();
    product_info_->good_product_num  = m_product_info["good_product_num"].toUInt();
    product_info_->bad_product_num   = m_product_info["bad_product_num"].toUInt();
    product_info_->bad_product_rate  = m_product_info["bad_product_rate"].toFloat();
    product_info_->process_time      = m_product_info["process_time"].toFloat();
    product_info_->aver_process_time = m_product_info["aver_process_time"].toFloat();
}

/**
 * @brief
 */
void CLenAdjustOpt::varibleInit() {
    //* 运行状态
    mc_processList_->taskInit(&mv_task_list, mst_task_status_);
    m_serial_thread_->task_id_change(4);
    mst_task_status_->auto_normal_mode = true;

    mst_task_status_->cur_step        = eCOMPLETE;
    mst_task_status_->cur_exec_status = eWAIT;
    mst_task_status_->cur_ack_status  = eWAIT;
    mst_task_status_->last_step       = eCOMPLETE;
    mst_task_status_->last_status     = eWAIT;

    //* 原光斑数据
    m_origin_bytes.clear();

    //    m_map_data_cache.clear();
    mc_facula_context_->mergeDataClean();

    //* 设备移动数据
    //  mst_device_info_->z_direction = m_xml_param["default_z_direct"];
    clenMoveDelta();

    //* result data
    mst_result_->chip_id.clear();
    mst_result_->origin_loc.clear();
    mst_result_->final_loc.clear();
    mst_result_->fatal_reason.clear();

    mst_dds_->process_item_dds.errors = 0;
    mst_dds_->process_step_dds        = 0;
    //    mst_dds_->adjust_step_dds = 0;
    //    mst_dds_->facula_judge_dds = 0;
    mst_dds_->mes_dds = 0;

    mst_mes_xsub2_data_->nbr      = "";
    mst_mes_xsub2_data_->nbr_pre  = "";
    mst_mes_xsub2_data_->rslt     = false;
    mst_mes_xsub2_data_->rsn_code = "";
    mst_mes_xsub2_data_->param[0] = 0;
    mst_mes_xsub2_data_->param[1] = 0;
    mst_mes_xsub2_data_->param[2] = 0;
    mst_mes_xsub2_data_->param[3] = 0;
    mst_mes_xsub2_data_->param[4] = 0;
    mst_mes_xsub2_data_->param[5] = 0;
    mst_mes_xsub2_data_->param[6] = 0;
    mst_mes_xsub2_data_->param[7] = 0;
    mst_mes_xsub2_data_->param[8] = 0;
    //    mst_mes_xsub2_data_->

    resultInit(&mst_result_->adjustResult);
    resultInit(&mst_result_->solidResult);
    resultInit(&mst_result_->finalResult);
    //  resultShow(EExecStatus::wait);

    mm_result_data["终止坐标x"] = "";
    mm_result_data["终止坐标y"] = "";
    mm_result_data["终止坐标z"] = "";

    mm_result_data["放气光斑code"] = "";
    mm_result_data["fatal reason"] = "";
}

void CLenAdjustOpt::mapDataShow() {
    IFaculaAdjust::StMapData map_data  = mc_facula_context_->getOriginMapData();
    IFaculaAdjust::StMapData map_data1 = mc_facula_context_->getExpandMapData();

    emit dataAckSignal(map_data.max_peak, map_data.map_matrix, map_data1.max_peak, map_data1.map_matrix);
}

/**
 * @brief CLenAdjustOpt::portlistUpdate
 * @return
 */
bool CLenAdjustOpt::portlistUpdate(void) {
    QStringList *port_list_ = new QStringList;

    bool port_flag = mi_icomm_->scanPort(port_list_, mst_config_->cur_port_name);  //列表名称变化
    emit portUpdateSignal(port_list_, port_flag);

    port_flag = mi_icomm_dev_->scanPort(port_list_, mst_config_->cur_dev_port_name);  //列表名称变化
    emit devicePortUpdateSignal(port_list_, port_flag);

    return true;
}

ITable::StTableInfo CLenAdjustOpt::getOriginTableInfo() {
    ITable::StTableInfo table_info;
    table_info.x           = mst_facula_sensor_info.sensor_info.xlens;
    table_info.y           = mst_facula_sensor_info.sensor_info.ylens;
    table_info.mp_order    = mst_facula_sensor_info.sensor_info.order;
    table_info.target_mp_x = mst_facula_sensor_info.target_tf.ax;
    table_info.target_mp_y = mst_facula_sensor_info.target_tf.ay;

    return table_info;
}

ITable::StTableInfo CLenAdjustOpt::getExpandTableInfo() {
    ITable::StTableInfo      table_info;
    IFaculaAdjust::StMapInfo map_info = mc_facula_context_->getExpandMapInfo();

    table_info.x           = map_info.xlens;
    table_info.y           = map_info.ylens;
    table_info.mp_order    = mst_facula_sensor_info.sensor_info.order;
    table_info.target_mp_x = map_info.target_tf.ax;
    table_info.target_mp_y = map_info.target_tf.ay;

    return table_info;
}


/**
 * @brief CLenAdjustOpt::dataReceive
 * @param step
 * @param status
 * @param bytes
 */
void CLenAdjustOpt::dataReceive(IClensMachine::ECommAck step, ECommStatus status, QByteArray bytes) {
    uint16_t num                  = bytes.length() >> 2;
    mst_comm_status_->comm_status = status;
    switch (step) {
    case IClensMachine::eLEN_CAP_ACK:
        mv_task_list[eLEN_CAP].flag.stop = true;
        break;

    case IClensMachine::eLEN_STATUS_ACK:
        if (mst_task_status_->cur_step == eLEN_STATUS) {
            mv_task_list[eLEN_STATUS].flag.stop = true;
        }
        break;

    case IClensMachine::eAUTO_ADJUST_ACK:
        mv_task_list[eVISION_ADJUST].flag.stop = true;

        break;

    case IClensMachine::eAUTO_ADJUST_STATUS_ACK:
        mv_task_list[eGET_VISION_STATUS].flag.stop = true;
        break;

    case IClensMachine::eADJUST_LEN_ACK:
        if (mst_task_status_->cur_step == eADJUST_LEN) {
            mv_task_list[eADJUST_LEN].flag.stop = true;
        }
        break;

    case IClensMachine::eMOVE_STATUS_ACK:
        if (mst_task_status_->cur_step == eGET_MOVE_STATUS)
            mv_task_list[eGET_MOVE_STATUS].flag.stop = true;
        break;

    case IClensMachine::eSOLID_ACK:
        mv_task_list[eSOLID].flag.stop = true;
        break;
    case IClensMachine::eSOLID_STATUS_ACK:
        mv_task_list[eSOLID_STATUS].flag.stop = true;
        break;
    case IClensMachine::eDEFLATE_ACK:
        mv_task_list[eDEFLATE].flag.stop = true;
        break;

    case IClensMachine::eEXIT_ACK:
        mv_task_list[eCOMPLETE].flag.stop = true;
        break;

    default:
        break;
    }
}

/**
 * @brief CLenAdjustOpt::dataReceive
 * @param step
 * @param status
 * @param bytes
 */
void CLenAdjustOpt::dataReceive(IClensMachine::ECommAck step, ECommStatus status, QVector<float> buff) {
    mst_comm_status_->comm_status = status;
    switch (step) {
    case IClensMachine::eLOC_ACK:
        m_loc_cache = buff;  // 缓存数据
        if (mst_task_status_->cur_step == eORIGIN_LOC)
            mv_task_list[eORIGIN_LOC].flag.stop = true;  //直接getLoc获取
        else
            mv_task_list[eADJUSTED_LOC].flag.stop = true;
        break;
    default:
        break;
    }
}

/**
 * @brief CLenAdjustOpt::dataRecive
 * @param step
 * @param status
 * @param bytes
 */
void CLenAdjustOpt::sensorDataReceive(ITopBoard::ECommStep step, ECommStatus status, QByteArray bytes) {
    uint16_t num = bytes.length() >> 2;

    mst_comm_status_->comm_status = status;
    if (num != 0) {
#if 0
        if(m_origin_bytes.length() == 0) m_origin_bytes = bytes;
        else qDebug() << "-clen: data receive busy";
#else
        m_origin_bytes = bytes;  //直接覆盖
#endif
    }

    switch (step) {
    case ITopBoard::eMODE_CHANGE:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eGREY_MODE:
            mv_task_list[eGREY_MODE].flag.stop = true;
            break;
        case EProcessStep::eCALIB_MODE:
            mv_task_list[eCALIB_MODE].flag.stop = true;
            break;
        default:
            break;
        }
        break;
    case ITopBoard::eCHIP_ID:
        mv_task_list[eCHIP_ID].flag.stop = true;
        break;
    case ITopBoard::eVERSION:
        mv_task_list[eVERSION].flag.stop = true;
        break;
    case ITopBoard::eMAP_DATA:
        switch (mst_task_status_->cur_step) {
        case EProcessStep::eMAP_DATA:
            mv_task_list[eMAP_DATA].flag.stop = true;
            break;
        case EProcessStep::eTEST:
            mv_task_list[eTEST].flag.stop = true;
            break;
        case EProcessStep::eRETEST:
            mv_task_list[eRETEST].flag.stop = true;
            break;
        case EProcessStep::eRETEST2:
            mv_task_list[eRETEST2].flag.stop = true;
            break;
        default:
            break;
        }
        break;
    default:
        break;
    }
}

void CLenAdjustOpt::sleepMs(uint16_t msec) {
    // QEventLoop eloop;
    // QTimer::singleShot(cnt, &eloop, SLOT(quit));  //&QEventLoop::quit);
    // eloop.exec();

    QTime dieTime = QTime::currentTime().addMSecs(msec);
    while (QTime::currentTime() < dieTime)
        QCoreApplication::processEvents(QEventLoop::AllEvents, 100);
}

void CLenAdjustOpt::sleepMsNonBlocking(uint16_t cnt, std::function<void()> callback) {
    QTimer::singleShot(cnt, this, [callback]() {
        if (callback) {
            callback();
        }
    });
}
///**
// * @brief: 数据合并，均值，中值
// * @param: 原值，合并个数，处理方式
// * @result: 处理结果
// */
// QVector<uint32_t> CLenAdjustOpt::dataMerge(const QVector<uint32_t> &origin_data, const uint8_t &times, const uint8_t &types)
//{
//    uint32_t data_tmp = 0;
//    QVector<uint32_t> data;
//    uint8_t num;
//    if(times == 0)
//        return data;
//    num = origin_data.length()/times;
//    if((num * times) != origin_data.length())
//        return data;
//    if(types == 1) {
//        for (uint16_t n = 0; n < num; ++n) {
//            for(uint8_t j = 0; j < times; ++j)
//                data_tmp += origin_data.at(j*num + n);
//            data_tmp = data_tmp/times;
//            data.push_back(data_tmp);
//            data_tmp = 0;
//        }
//    }
//    return data;
//}

/**
 * @brief 光斑数据检测
 * @return
 */
// bool CLenAdjustOpt::mapTypeCheck(const QByteArray &origin_map_data)
//{
//  uint16_t num = origin_map_data.length() >> 2;


//  return true;
//}

/**
 * @brief 数据合并处理
 * @return
 */
// bool CLenAdjustOpt::mapDataMerge(QByteArray &mp_origin_byte, StMergeData* merge_data_, QVector<uint32_t>* map_data_)
//{
//    uint16_t num = mp_origin_byte.length() >> 2;

//    if(merge_data_->merge_data_cnt >= DISCARD_PACK_NUM) {//舍弃前几包
//        for(uint16_t n = 0; n < num; ++n) {//
//            uint32_t data_tmp = (uchar)mp_origin_byte.at((n<<2) + 0) | ((uchar)mp_origin_byte.at((n<<2) + 1)<<8) | ((uchar)mp_origin_byte.at((n<<2) + 2)<<16)
//            | ((uchar)mp_origin_byte.at((n<<2) + 3)<<24);

//            merge_data_->map_data_cache.push_back(data_tmp); //
//        }
//    }

//    merge_data_->merge_data_cnt++;
//    mp_origin_byte.clear();

//    if(merge_data_->merge_data_cnt == (MP_MERGE_TIMES + DISCARD_PACK_NUM)) {//MP_MERGE_TIMES
//        *map_data_ = dataMerge(merge_data_->map_data_cache, (uint8_t)MP_MERGE_TIMES, 1); //

//        merge_data_->merge_data_cnt = 0;
//        merge_data_->map_data_cache.clear();

//        return true;
//    }

//    return false;
//}

/**
 * @brief CLenAdjustOpt::originDataHanlde
 * @param mp_origin_bytes -> 光斑矩阵数据
 * @return
 */
// bool CLenAdjustOpt::originDataHanlde(QByteArray &mp_origin_bytes, IFaculaAdjust::StMapData* map_data_)
//{
//    //    static uint16_t cell_num_last = 0; //传入MP数
//    //    static uint8_t  merge_data_cnt = 0; //传入数据帧个数
//    uint16_t num = mp_origin_bytes.length() >> 2;

//    QVector<uint32_t> map_data; //

//    if((num == 0) || (num != (mst_map_info_->xlens * mst_map_info_->ylens))) {
//        mergeDataClean();
//        return false;
//    }
//    //* 判断数据有效性
//    //    if((num > 0) && num != m_cell_num_first) {//单元格更新
//    //        m_cell_num_first = num;

//    //        mergeDataClean();
//    ////        merge_data_cnt = 0;
//    ////        m_map_data_cache.clear();
//    //        if(!cellNumUpdate(num)) return false; //
//    //    }

//    if(mapDataMerge(mp_origin_bytes, mst_merge_data_, &map_data)) {//数据合并完成
//        uint8_t ylen = mst_map_info_->ylens;
//        uint8_t xlen = mst_map_info_->xlens;

//        if(map_data.length() != ylen*xlen) {
//            qDebug() << "-e clen/ map data length error" << map_data.length() << ylen*xlen;
//            return false;
//        }

//        //* 添加数据,
//        if(mst_map_info_->mp_order == IPhotonSensor::EMpOrder::left_2_right) { //光斑顺序 左->右 YC
//            for (int y=0; y<ylen; ++y) {
//                map_data_->map_matrix[y].clear();
//                for (int x=0; x<xlen; ++x) {
//                    map_data_->map_matrix[y].push_back(map_data.at((y*xlen) + x));
//                }
//            }
//        }
//        else {//上->下 4300 4302
//            for(int i = 0; i<ylen; ++i) {
//                map_data_->map_matrix[i].clear(); //;
//            }
//            for(int x=0; x<xlen; ++x) {
//                for (int y=0; y<ylen; ++y) {
//                    map_data_->map_matrix[y].push_back(map_data.at((x*ylen) + y));
//                }
//            }
//        }
//        return true;
//    }
//    return false;
//}

/**
 * @brief 根据单帧 数据长度 切换接收数据类型
 * @param cell_num
 * @return
 */
// bool CLenAdjustOpt::cellNumUpdate(const uint16_t &cell_num)
//{
//    bool result = true;
//    uint8_t xlen, ylen;

//    if(IPhotonSensor::mm_mpNum_link_dimension.find(cell_num) != IPhotonSensor::mm_mpNum_link_dimension.end()) {
//        /*1. 表格尺寸更新*/
//        xlen = IPhotonSensor::mm_mpNum_link_dimension[cell_num].x;
//        ylen = IPhotonSensor::mm_mpNum_link_dimension[cell_num].y;

//        mst_map_info_->mp_order = IPhotonSensor::mm_mpNum_link_dimension[cell_num].order;
//        mst_map_info_->sensor_direction = IPhotonSensor::mm_mpNum_link_dimension[cell_num].direct;
////        mst_target_map_info_->sensor_direction = IPhotonSensor::mm_mpNum_link_dimension[cell_num].direct;
//    }
//    else {
//        qDebug() << qLogOpt::logCommands[qLogOpt::enumToInt(qLogOpt::LogType::FrontRed)] << "-e len/ map num:" << cell_num;
//        //        m_map_data.clear();
//        return false;
//    }

//    /*1.1 原表格配置*/
//    m_map_data_->map_matrix.resize(ylen); //
//    mst_map_info_->xlens = xlen;
//    mst_map_info_->ylens = ylen;

//    /*1.2 拓展表格配置*/
//    xlen += xlen%2 == 0?1:2;
//    ylen += ylen%2 == 0?1:2;

//    m_map_interpolation_data_->map_matrix.resize(ylen); //
//    for (uint i = 0; i < ylen; i++) {
//        m_map_interpolation_data_->map_matrix[i].resize(xlen);
//    }
//    mst_interpolation_map_info_->xlens = xlen;
//    mst_interpolation_map_info_->ylens = ylen;

//    //* 2.目标区域通道 更新
//    result &= targetFaculaArea(mst_map_info_);
////    mc_facula_context_->targetMapUpdate(*mst_target_map_info_);
//    result &= targetFaculaArea(mst_interpolation_map_info_);

//    emit mainMapSizeSignal(mst_map_info_->xlens, mst_map_info_->ylens);
//    return result;
//}

/**
 * @brief CLenAdjustOpt::targetFaculaArea
 * @param 自定义 facula location
 * @param map_info_
 * @return
 */
// bool CLenAdjustOpt::targetFaculaArea(const QString &facula_loc, IFaculaAdjust::StMapInfo *map_info_, IFaculaAdjust::StMapTargetInfo *target_info_)
//{
//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    if(xlens < 3 || ylens < 2) {
//        return false;
//    }
//    else {
//        uint8_t ax = ((xlens + 1) >>1) - 1; //中心点 TF
//        uint8_t ay = ((ylens + 1) >>1) - 1; //
//    }
//    return true;
//}

/**
 * @brief: 目标光斑区域解析-二维坐标轴, 横轴X，纵轴Y，左上角（0，0）
 * @param:
 */
// bool CLenAdjustOpt::targetFaculaArea(IFaculaAdjust::StMapInfo *map_info_)
//{
//    uint8_t xlens = map_info_->xlens;
//    uint8_t ylens = map_info_->ylens;
//    if(xlens < 3 || ylens < 2) {
//        return false;
//    }
//    else {
//        uint8_t ax = ((xlens + 1) >>1) - 1; //中心点 TF
//        uint8_t ay = ((ylens + 1) >>1) - 1; //

//        switch (map_info_->facula_loc) {
//        case EFaculaLoc::AlignLeft:
//            map_info_->target_tf.ax = 0; //
//            map_info_->target_tf.ay = ((ylens + 1) >>1) - 1; //
//            break;
//        case EFaculaLoc::AlignRight:
//            map_info_->target_tf.ax = xlens - 1; //
//            map_info_->target_tf.ay = ((ylens + 1) >>1) - 1; //
//            break;
//        case EFaculaLoc::AlignCenter:
//            map_info_->target_tf.ax = ((xlens + 1) >>1) - 1; //
//            map_info_->target_tf.ay = ((ylens + 1) >>1) - 1; //
//        default:
//            break;
//        }
//    }
//    return true;
//}

void CLenAdjustOpt::clenMoveDelta(void) {
    mi_clen_machine_->cleanMoveDistance();
}

QString CLenAdjustOpt::sensorVersionParse(const QByteArray &version) {
    if (version.size() < 4)
        return "";
    QString tr_version = QString::number(version.at(0), 10) + "." + QString::number(version.at(1), 10) + "." + QString::number(version.at(2), 10) + "." +
                         QString::number(version.at(3), 10);
    return tr_version;
}

bool CLenAdjustOpt::checkVersion(const QString &version) {
    if (version != mst_iniConfig.version)
        return false;
    return true;
}

bool CLenAdjustOpt::mesDataHandle(CLenSqlHandle::StResultToMes *st_mes_data_) {
    st_mes_data_->date = QString(QDateTime::currentDateTime().toString("yyyy/MM/dd"));

    st_mes_data_->time = QDateTime::currentDateTime().time().msecsSinceStartOfDay() / 1000;  // s
    st_mes_data_->rslt = (mst_result_->finalResult.result == 0) ? true : false;              //(!mst_result_->finalResult.result) == true;
    if (st_mes_data_->rslt)
        st_mes_data_->rsn_code = "";
    else
        st_mes_data_->rsn_code = QString::number(mst_result_->finalResult.result, 16);  // mm_result_data["fatal reason"].toString();
    //    st_mes_data_->rslt_pre = ;

    //    if(mst_result_->finalResult.map_matrix.size() > 3 && mst_result_->finalResult.map_matrix.at(0).size() > 3) {
    //        st_mes_data_->param[0] = mst_result_->finalResult.map_matrix.at(1).at(1);
    //        st_mes_data_->param[1] = mst_result_->finalResult.map_matrix.at(1).at(2);
    //        st_mes_data_->param[2] = mst_result_->finalResult.map_matrix.at(1).at(3);
    //        st_mes_data_->param[3] = mst_result_->finalResult.map_matrix.at(2).at(1);
    //        st_mes_data_->param[4] = mst_result_->finalResult.map_matrix.at(2).at(2);
    //        st_mes_data_->param[5] = mst_result_->finalResult.map_matrix.at(2).at(3);
    //        st_mes_data_->param[6] = mst_result_->finalResult.map_matrix.at(3).at(1);
    //        st_mes_data_->param[7] = mst_result_->finalResult.map_matrix.at(3).at(2);
    //        st_mes_data_->param[8] = mst_result_->finalResult.map_matrix.at(3).at(3);
    //    }
    uint8_t target_map_cnt = mst_result_->target_map.count();
    if (target_map_cnt == 9) {
        for (uint8_t for_i = 0; for_i < target_map_cnt; for_i++) {
            st_mes_data_->param[for_i] = mst_result_->target_map.at(for_i);
        }
    } else {
        // COMP_LOG_ERROR(logger, MyLogger::LogType::ERROR_LOG, "Target map count error: %d", target_map_cnt);
    }
    return true;
}


QString CLenAdjustOpt::errorInfoPack(EError_type error_type, const uint32_t &error_code) {
    QString   error_info;
    QMetaEnum stepEnum = QMetaEnum::fromType<CLenAdjustOpt::EProcessStep>();
    switch (error_type) {
    case EError_type::ePROCESS_STEP_ERROR:
        for (uint32_t for_i = 0; for_i <= (uint16_t)eCOMPLETE; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            QString  step_str    = stepEnum.valueToKey(for_i);
            if (error_index != 0)
                error_info += "task: " + step_str + " fatal";
        }
        break;
    case EError_type::ePROCESS_ITEM_ERROR:
        //        error_info = "过程：";
        for (uint32_t for_i = 0; for_i < 32; for_i++) {
            uint32_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += QString::number(for_i, 10) + "." + mm_process_items_discribe[error_index];
        }
        break;
    case EError_type::eADJUST_STEP_ERROR:
        //        error_info = "光斑调节：";
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += QString::number(for_i, 10) + "." + IFaculaAdjust::mm_adjust_step_discribe[error_index];
        }
        break;

    case EError_type::eFACULA_JUDGE_ERRPR:
        //        error_info = "光斑判定：";
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += "光斑判定：" + QString::number(for_i, 10) + "." + IFaculaAdjust::mm_facula_judge_discribe[error_index];
        }
        break;

    case EError_type::eMES_ERROR:
        for (uint32_t for_i = 0; for_i < 16; for_i++) {
            uint16_t error_index = error_code & (1 << for_i);
            if (error_index != 0)
                error_info += "MES：" + QString::number(for_i, 10) + "." + CLenSqlHandle::mm_mes_error_discribe[error_index];
        }

        break;
    default:
        break;
    }

    return error_info;
}

EExecStatus CLenAdjustOpt::readyStep(void) {
    //* 端口信息变更，（不包括波特率）
    if (mi_icomm_->getPortName() != mst_config_->port_name) {
        if (mi_icomm_ != nullptr)
            delete mi_icomm_;
        mi_icomm_ = new CUART(mst_config_->port_name, mst_iniConfig.sensor_device_buad);

        mi_top_board_->icom_change_interface(mi_icomm_);
        //        m_serial_thread_->device_change_interface(mi_top_board_, nullptr);
    }

    if (mi_icomm_dev_->getPortName() != mst_config_->dev_port_name) {
        if (mi_icomm_dev_ != nullptr)
            delete mi_icomm_dev_;
        mi_icomm_dev_ = new CUART(mst_config_->dev_port_name, DEVICE_UART_BAUD);

        mi_clen_machine_->icom_change_interface(mi_icomm_dev_);
        //        m_serial_thread_->device_change_interface(nullptr, mi_clen_machine_);
    }

    //* 端口检测
    if ((!mi_icomm_->openPort()) || (!mi_icomm_dev_->openPort())) {
        mst_dds_->process_item_dds.all_error.serial_open = 1;
        emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        return eFATAL;
    }

    //* varibles init
    varibleInit();

    //* 接收线程
    emit subThreadSignal(true);
    emit readySignal(true);

    return eCOMP;
}

EExecStatus CLenAdjustOpt::readyAck(void) {
    QVector<uint16_t> data;

    if (!mst_config_->mode) {
        if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES

            mst_dds_->mes_dds = mc_sql_handle_->checkWorkOrder(mst_work_order_);
            QString info      = "工单号: " + mst_work_order_->wo_lot;
            emit    moduleInfoShowSignal(false, info);

            if (mst_dds_->mes_dds != 0) {
                QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
                emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
                return eFATAL;
            }
        }
        return eCOMP;
    } else {
        return eWAIT;  // manual mode not execute cap step
    }
}

/**
 * @brief 任务开始:抓取镜片
 * @return
 */
EExecStatus CLenAdjustOpt::startStep(void) {
    //* 开始指令
    QVector<uint16_t> data;
    //     if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES

    //         mst_dds_->mes_dds = mc_sql_handle_->checkWorkOrder(mst_work_order_);
    //         QString info      = "工单号: " + mst_work_order_->wo_lot;
    //         emit    moduleInfoShowSignal(false, info);

    //         if (mst_dds_->mes_dds == 0) {
    //             if (mi_clen_machine_->start(data)) {
    // #if BLOCK_LEN_MACHINE
    //                 return eCOMP;
    // #else
    //                 m_serial_thread_->task_id_change(2);
    //                 return eOK;
    // #endif
    //             } else {
    //                 return eFATAL;  //发送失败->不可恢复错误
    //             }
    //         } else {
    //             QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
    //             emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
    //             return eFATAL;
    //         }
    //     } else {
    if (mi_clen_machine_->start(data)) {
#if BLOCK_LEN_MACHINE
        return eCOMP;
#else
        m_serial_thread_->task_id_change(2);
        return eOK;
#endif
    } else {
        return eFATAL;  //发送失败->不可恢复错误
    }
    // }
    // return eOK;
}

/**
 * @brief 开始指令反馈
 */
EExecStatus CLenAdjustOpt::startAck(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        emit startAckSignal();
        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
#endif
}


EExecStatus CLenAdjustOpt::calibMode(void) {
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofCalibrationMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}


EExecStatus CLenAdjustOpt::calibModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        //      m_serial_thread_->task_id_change(4);
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}


EExecStatus CLenAdjustOpt::chipIdStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kMcuId, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus CLenAdjustOpt::chipIdAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //      m_serial_thread_->task_id_change(4);

        mst_result_->chip_id = m_origin_bytes.toHex().toUpper();
        m_origin_bytes.clear();

        if (mst_result_->chip_id == "")
            mst_dds_->process_item_dds.all_error.chip_id = 1;
        LOG_DEBUG(MyLogger::LogType::PROCESS_STATUS, QString("MCUID: %1").arg(mst_result_->chip_id));
        QString info = QString("chip id:" + mst_result_->chip_id + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit    moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.chip_id, info);

        if (mst_dds_->process_item_dds.all_error.chip_id)
            return eFATAL;
        else {
            if (!mst_config_->mode)
                return eCOMP;
            else
                return eWAIT;
        }
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

EExecStatus CLenAdjustOpt::sensorVersionStep(void) {
    //* 校正模式
    if (!mi_top_board_->readInfo(CSensorCoinD::kVersionBuad, 0))
        return eFATAL;
    else {
        m_serial_thread_->task_id_change(3);
        return eOK;
    }
}


EExecStatus CLenAdjustOpt::sensorVersionAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        mst_result_->sensor_version = sensorVersionParse(m_origin_bytes);
        m_origin_bytes.clear();

        if (!checkVersion(mst_result_->sensor_version))
            mst_dds_->process_item_dds.all_error.version = 1;  //版本异常如何处理

        QString info =
            QString("version: " + mst_result_->sensor_version + "\n" + errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
        emit moduleInfoShowSignal(mst_dds_->process_item_dds.all_error.version, info);

        if (mst_dds_->process_item_dds.all_error.version)
            return eFATAL;
        else {
            if (!mst_config_->mode) {  //自动
                //* mes connect
                // check work_number status(工单状态)
                if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES
                    mst_xpid_det_->mcuid      = mst_result_->chip_id;
                    mst_xpid_det_->work_order = mst_work_order_->wo_lot;
                    mst_xpid_det_->domain     = mst_work_order_->domain;
                    mst_dds_->mes_dds |= mc_sql_handle_->searchTagNumber(*mst_xpid_det_, mst_mes_xsub2_data_->nbr, true);
                    if (mst_dds_->mes_dds != 0) {
                        emit moduleInfoShowSignal(true, errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds));
                        return eFATAL;
                    }
                }
                return eCOMP;
            } else
                return eWAIT;

            return eCOMP;
        }
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief greyMapMode
 * @return
 */
EExecStatus CLenAdjustOpt::greyMapMode(void) {
    //* 光斑模式
    if (mi_top_board_->unlockModule() && mi_top_board_->modeChange(CSensorCoinD::kTofFaculaMode)) {
        m_serial_thread_->task_id_change(3);
        return eOK;
    } else
        return eFATAL;
}

/**
 * @brief greyMapModeAck
 * @return
 */
EExecStatus CLenAdjustOpt::greyMapModeAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief 获取镜片抓取状态
 * @return
 */
EExecStatus CLenAdjustOpt::lensCapped(void) {
    if (mi_clen_machine_->getStatus()) {
#if BLOCK_LEN_MACHINE
        return eCOMP;
#else
        m_serial_thread_->task_id_change(2);
        return eOK;
#endif
    } else
        return eFATAL;
}

/**
 * @brief lensCompAck
 * @return
 */
EExecStatus CLenAdjustOpt::lensCappedAck(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        // if (!mst_config_->mode) {
        //     mc_facula_context_->varibleInit();  // facula find param init
        return eCOMP;
        // } else
        //     return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
#endif
}

/**
 * @brief 机台粗调任务开始
 * @return
 */
EExecStatus CLenAdjustOpt::lenMachineAutoAdjust(void) {
    //* 开始指令
    QVector<uint16_t> data;
    if (mst_iniConfig.userid != MES_SUPER_USER) {  //非测试账号，连接MES

        mst_dds_->mes_dds = mc_sql_handle_->checkWorkOrder(mst_work_order_);
        QString info      = "工单号: " + mst_work_order_->wo_lot;
        emit    moduleInfoShowSignal(false, info);

        if (mst_dds_->mes_dds == 0) {
            if (mi_clen_machine_->autoAdjust(data)) {
#if BLOCK_LEN_MACHINE
                return eCOMP;
#else
                m_serial_thread_->task_id_change(2);
                return eOK;
#endif
            } else {
                return eFATAL;  //发送失败->不可恢复错误
            }
        } else {
            QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
            emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
            return eFATAL;
        }
    } else {
        if (mi_clen_machine_->autoAdjust(data)) {
#if BLOCK_LEN_MACHINE
            return eCOMP;
#else
            m_serial_thread_->task_id_change(2);
            return eOK;
#endif
        } else {
            return eFATAL;  //发送失败->不可恢复错误
        }
    }
    return eOK;
}

/**
 * @brief
 */
EExecStatus CLenAdjustOpt::lenMachineAutoAdjustAck(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        emit startAckSignal();
        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
#endif
}


/**
 * @brief 获取机台调节状态
 * @return
 */
EExecStatus CLenAdjustOpt::lenMachineAdjustStatus(void) {
    if (mi_clen_machine_->getAutoAdjustStatus()) {
#if BLOCK_LEN_MACHINE
        return eCOMP;
#else
        m_serial_thread_->task_id_change(2);
        return eOK;
#endif
    } else
        return eFATAL;
}

/**
 * @brief lensCompAck
 * @return
 */
EExecStatus CLenAdjustOpt::lenMachineAdjustStatusAck(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        if (!mst_config_->mode) {
            mc_facula_context_->varibleInit();  // facula find param init
            return eCOMP;
        } else
            return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) {
        return eERROR;

    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_FATAL) {
        // 异常->直接固化
        emit unnormalViewSignal(true);
        //                    mc_facula_context_->mergeDataClean();
        QString error_info = "粗调异常";
        emit moduleInfoShowSignal(true, error_info);
        mst_result_->fatal_reason = error_info;
        if (mst_iniConfig.facula_ng_handle) {  //
            mv_task_list[eADJUSTED_LOC].flag.exec = true;
        }
        return eWAIT;

    } else {
        return eWAIT;
    }

#endif
}

EExecStatus CLenAdjustOpt::getOriginLoc() {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    uint8_t dimension = 0;  //轴向

    if (mi_clen_machine_->getLoc(dimension)) {
        m_serial_thread_->task_id_change(2);
        return eOK;
    } else
        return eFATAL;
#endif
}

EExecStatus CLenAdjustOpt::getOriginLocAck() {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        mst_result_->origin_loc = m_loc_cache;
        emit originLocSignal(mst_result_->origin_loc.at(0), mst_result_->origin_loc.at(2), mst_result_->origin_loc.at(1));
        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
#endif
}

/**
 * @brief 获取光斑数据
 * @return
 */
EExecStatus CLenAdjustOpt::dataStep(void) {
    m_serial_thread_->task_id_change(1);
    return eOK;
}

EExecStatus CLenAdjustOpt::dataAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {

        //* 循环接收->数据处理  原始数据->处理后的数据
        C3dHandMachine::St3D<int16_t> move_delta_step = mi_clen_machine_->getMoveStep();

        IFaculaAdjust::UFaculaAdjustDds adjust_status;
#if 0  // 启用测试数据进行验证
       //        QByteArray tmp(100, 2);
        uint8_t tmp[100] = {00, 00, 00, 00, 00, 00,   00, 00, 00, 00,   00,   00, 00, 00,   00,   00, 00, 00,   00,   00, 00, 00, 00, 00, 00,
                            00, 00, 00, 00, 00, 00,   00, 00, 00, 00,   00,   00, 00, 00,   00,   00, 00, 00,   00,   00, 00, 00, 00, 00, 00,
                            00, 00, 00, 00, 00, 00,   00, 00, 00, 00,   00,   00, 00, 00,   10,   00, 00, 00,   0x1b, 01, 00, 00, 00, 00, 00,
                            00, 00, 00, 00, 00, 0x03, 00, 00, 00, 0x5f, 0x01, 00, 00, 0x17, 0x03, 00, 00, 0xa3, 0x01, 00, 00, 00, 00, 00, 00};
        m_origin_bytes   = (char *)tmp;
        qDebug() << "[TEST] 启用了tmp[100]测试数据进行图像处理验证";
#endif
        if (mc_facula_context_->faculaHandle(adjust_status.adjust_step, m_origin_bytes, move_delta_step, mst_move_distance_)) {
            mapDataShow();

            if (!mst_config_->mode && mst_task_status_->auto_normal_mode) {
                m_serial_thread_->task_id_change(4);

                // if (adjust_status.adjust_step != 0) {
                //     emit unnormalViewSignal(true);

                //     if (adjust_status.all_step.retest) {  //调节完毕 || 继续执行
                //         mv_task_list[eADJUSTED_LOC].flag.exec = true;

                //         return eWAIT;
                //     } else {
                //         QString error_info = errorInfoPack(EError_type::eADJUST_STEP_ERROR, adjust_status.adjust_step);
                //         emit    moduleInfoShowSignal(true, error_info);
                //         mst_result_->fatal_reason = error_info;
                //         if (mst_iniConfig.facula_ng_handle) {  //
                //             mv_task_list[eADJUSTED_LOC].flag.exec = true;
                //         }
                //         return eWAIT;
                //     }
                // }
                return eCOMP;
            } else
                return eWAIT;
        } else
            return eOK;
        ;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}


EExecStatus CLenAdjustOpt::adjustStep(void) {
    static uint8_t move_error_cnt = 0;
    m_move_wait_times             = 0;

#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_facula_sensor_info.sensor_direction) {
        mst_move_distance_->y = -mst_move_distance_->y;
        mst_move_distance_->x = -mst_move_distance_->x;
    }
    uint8_t move_status = mi_clen_machine_->deviceSpeedHandle(*mst_move_distance_, m_move_wait_times);

    //    COMP_LOG_DEBUG(logger, MyLogger::LogType::PROCESS_STATUS, "Move status: %d", move_status);
    if ((move_status & 0x3f) != 0) {
        emit unnormalViewSignal(true);

        if ((move_status & (uint8_t)IClensMachine::EDeviceStatus::eXY_AXIS_LMIT) == (uint8_t)IClensMachine::EDeviceStatus::eXY_AXIS_LMIT) {
            mst_dds_->process_item_dds.all_error.xy_limit = 1;
        }
        if ((move_status & (uint8_t)IClensMachine::EDeviceStatus::eZ_AXIS_LMIT) == (uint8_t)IClensMachine::EDeviceStatus::eZ_AXIS_LMIT) {
            mst_dds_->process_item_dds.all_error.z_limit = 1;
        }
        emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
#if 0
        return eFATAL;
#else
        mv_task_list[eTEST].flag.exec = true;
        mc_facula_context_->mergeDataClean();
        return eWAIT;
#endif
    }
    //* 位移全是0，未发送
    if ((move_status & (uint8_t)IClensMachine::EDeviceStatus::eCMD_SEND) == (uint8_t)IClensMachine::EDeviceStatus::eCMD_SEND) {
        mv_task_list[eMAP_DATA].flag.exec = true;
        return eWAIT;  //
    } else {           //发送
        if ((move_status & (uint8_t)IClensMachine::EDeviceStatus::eCMD_STATUS) == (uint8_t)IClensMachine::EDeviceStatus::eCMD_STATUS) {
            m_serial_thread_->task_id_change(2);
            move_error_cnt = 0;
            return eOK;
        } else {
            if (++move_error_cnt > 3) {
                move_error_cnt = 0;

                emit unnormalViewSignal(true);
                mst_dds_->process_item_dds.all_error.device_move = 1;
                emit moduleInfoShowSignal(true, errorInfoPack(EError_type::ePROCESS_ITEM_ERROR, mst_dds_->process_item_dds.errors));
                //                mst_result_->fatal_reason = QString("move error code:" + QString::number(move_status, 10));

                if (mst_iniConfig.facula_ng_handle) {  //
                    emit unnormalViewSignal(true);
                    mv_task_list[eTEST].flag.exec = true;
                    mc_facula_context_->mergeDataClean();

                    return eWAIT;
                } else {
                    return eFATAL;
                }
            }
            return eERROR;
        }
    }
#endif
}

EExecStatus CLenAdjustOpt::adjustAck(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);

        // if (m_move_wait_times != 0) {  //等待移动
        //     m_move_wait_times--;
        //     //            COMP_LOG_DEBUG(logger, MyLogger::LogType::PROCESS_STATUS, "Move wait times: %d", m_move_wait_times);
        //     mv_task_list[CLenAdjustOpt::eADJUST_LEN].flag.stop = true;
        //     return eWAIT;
        // }
        // mv_task_list[eMAP_DATA].flag.exec = true;
        // mc_facula_context_->mergeDataClean();

        // C3dHandMachine::St3D<int16_t> move_dist = mi_clen_machine_->getMoveDist();
        // emit                          adjustAckSignal(move_dist.x, move_dist.y, move_dist.z);
        return eCOMP;  //
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) {
        return eERROR;
    } else {
        return eWAIT;
    }
#endif
}

/**
 * @brief 获取机台调节状态
 * @return
 */
EExecStatus CLenAdjustOpt::getMoveStatusStep(void) {
#if BLOCK_LEN_MACHINE
    return eCOMP;
#else
    if (mi_clen_machine_->getMoveStatus()) {
        m_serial_thread_->task_id_change(2);
        return eOK;
    } else {
        return eFATAL;
    }
#endif
}

/**
 * @brief lensCompAck
 * @return
 */
EExecStatus CLenAdjustOpt::getMoveStatusAck(void) {
#if BLOCK_LEN_MACHINE
    mv_task_list[eMAP_DATA].flag.exec = true;
    mc_facula_context_->mergeDataClean();
    return eWAIT;
#else
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);

        mv_task_list[eMAP_DATA].flag.exec = true;
        mc_facula_context_->mergeDataClean();

        C3dHandMachine::St3D<int16_t> move_dist = mi_clen_machine_->getMoveDist();
        emit adjustAckSignal(move_dist.x, move_dist.y, move_dist.z);
        return eWAIT;  //
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR) {
        return eERROR;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_TIMEOUT) {
        // 异常->直接固化
        emit unnormalViewSignal(true);

        QString error_info = "机台移动超时";
        emit moduleInfoShowSignal(true, error_info);
        mst_result_->fatal_reason = error_info;
        if (mst_iniConfig.facula_ng_handle) {
            mv_task_list[eADJUSTED_LOC].flag.exec = true;
        }
        return eWAIT;
    } else {
        return eWAIT;
    }
#endif
}

EExecStatus CLenAdjustOpt::getAdjustedLoc() {
    uint8_t dimension = 0;

    if (mi_clen_machine_->getLoc(dimension)) {
        m_serial_thread_->task_id_change(2);
        return eOK;
    } else
        return eFATAL;
}

EExecStatus CLenAdjustOpt::getAdjustedLocAck() {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);

        mst_result_->final_loc = m_loc_cache;
        emit adjustedLocSignal(mst_result_->final_loc.at(0), mst_result_->final_loc.at(2), mst_result_->final_loc.at(1));

        mc_facula_context_->mergeDataClean();

#if 0  // for test
//        mv_task_list[eMAP_DATA].flag.exec = true;

        mst_task_status_->auto_normal_mode = false;

        COMP_LOG_DEBUG(logger, MyLogger::LogType::PROCESS_STATUS, "Got location");
        return eWAIT;
#endif
        return eCOMP;
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief 光斑验证
 * @return
 */
EExecStatus CLenAdjustOpt::testStep(void) {
    m_serial_thread_->task_id_change(1);
    return eOK;
}

/**
 * @brief 光斑验证
 * @return
 */
EExecStatus CLenAdjustOpt::testAck(void) {
    static uint8_t                 facula_test_num  = 0;
    QByteArray                     origin_bytes_tmp = m_origin_bytes;
    QVector<uint32_t>              target_map;
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;

    if (mc_facula_context_->faculaTest(m_origin_bytes, facula_dds_info.dds_info, target_map, IFaculaAdjust::eFACULA_ADJUST_TEST, true)) {
        mapDataShow();

        mst_result_->adjustResult.result &= facula_dds_info.dds_info;
        if (mst_result_->adjustResult.result != 0) {
            mst_result_->adjustResult.mp_origin_data = origin_bytes_tmp;
        }

        //        qDebug() << "-i clens/ test1 result code:" << mst_result_->adjustResult.result;
        mst_result_->adjustResult.map_matrix = mc_facula_context_->getMap();

        facula_test_num++;
        if (facula_test_num >= mst_iniConfig.facula_ok_times) {
            facula_test_num = 0;
            m_serial_thread_->task_id_change(4);

            emit resultSignal(EResult(!mst_result_->adjustResult.result), 0);
            if (mst_result_->adjustResult.result == 0 || mst_iniConfig.facula_ng_handle) {
                mst_result_->adjustResult.mp_origin_data = origin_bytes_tmp;
                return eCOMP;
            } else {
                mst_task_status_->auto_normal_mode = false;
                mv_task_list[eMAP_DATA].flag.exec  = true;
                return eWAIT;  //手动处理
            }
        }
    }
    return eOK;
}

EExecStatus CLenAdjustOpt::solidStep(void) {
    if (mi_clen_machine_->solid()) {
        m_serial_thread_->task_id_change(2);
        return eOK;
    } else
        return eFATAL;
}

EExecStatus CLenAdjustOpt::solidAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);

        mc_facula_context_->mergeDataClean();
        //        if (!mst_config_->mode)
        return eCOMP;
        //        else
        //            return eWAIT;  //
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
    //    return eCOMP;
}

/**
 * @brief 获取固化状态
 * @return
 */
EExecStatus CLenAdjustOpt::solidStatusStep(void) {
    if (mi_clen_machine_->getStatus()) {
        m_serial_thread_->task_id_change(2);
        return eOK;  // eOK;
    } else {
        return eFATAL;
    }
}

/**
 * @brief 反馈
 * @return
 */
EExecStatus CLenAdjustOpt::solidStatusAck(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);
        mc_facula_context_->mergeDataClean();

        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;  //
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
    return eCOMP;
}

/**
 * @brief 固化后复测
 * @return
 */
EExecStatus CLenAdjustOpt::retestStep(void) {
    static uint16_t s_deflate_cnt = 0;

    if ((s_deflate_cnt++) > m_solid_times) {  //等待固化结束

        s_deflate_cnt = 0;

        m_serial_thread_->task_id_change(1);
        return eOK;
    } else
        return eERROR;
}

/**
 * @brief 固化后复测
 * @return
 */
EExecStatus CLenAdjustOpt::retestAck(void) {
    static uint8_t                 facula_test1_num = 0;
    QByteArray                     origin_bytes_tmp = m_origin_bytes;
    QVector<uint32_t>              target_map;
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;

    if (mc_facula_context_->faculaTest(m_origin_bytes, facula_dds_info.dds_info, target_map, IFaculaAdjust::eFACULA_SOLID_TEST, true)) {
        //        emit dataAckSignal();
        mapDataShow();

        mst_result_->solidResult.result &= facula_dds_info.dds_info;
        mst_result_->solidResult.map_matrix = mc_facula_context_->getMap();
        if (mst_result_->solidResult.result != 0) {
            mst_result_->solidResult.mp_origin_data = origin_bytes_tmp;
        }

        facula_test1_num++;
        if (facula_test1_num >= mst_iniConfig.facula_ok_times) {
            facula_test1_num = 0;
            m_serial_thread_->task_id_change(4);

            emit resultSignal(EResult(!mst_result_->solidResult.result), 1);

            if (mst_result_->solidResult.result == 0 || mst_iniConfig.facula_ng_handle) {
                mst_result_->solidResult.mp_origin_data = origin_bytes_tmp;
                return eCOMP;  //下一步
            } else
                return eWAIT;
        }
    }

    return eWAIT;
}

/**
 * @brief 放气与 Z轴上移10*9um
 * @return
 */
EExecStatus CLenAdjustOpt::deflateStep(void) {
    C3dHandMachine::St3D<int16_t> move_step;
    //  static uint8_t delay_cnt = 100;
    move_step.x = 0;
    move_step.y = 0;
    move_step.z = -10;

    m_serial_thread_->task_id_change(2);

    //  if(mi_clen_machine_->deflate() && mi_clen_machine_->adjust(move_step)) return eOK;
    if (mi_clen_machine_->deflate()) {
        //      pthread_delay_np();
        sleepMs(50);
        if (mi_clen_machine_->adjust(move_step))
            return eWAIT;
        else
            return eFATAL;
    } else {
        return eFATAL;
    }
}

/**
 * @brief deflateACK
 * @return
 */
EExecStatus CLenAdjustOpt::deflateACK(void) {
    if (mst_comm_status_->comm_status == ECommStatus::eCOMM_OK)
        return eOK;
    else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_COMP) {
        m_serial_thread_->task_id_change(4);

        mc_facula_context_->mergeDataClean();
        if (!mst_config_->mode)
            return eCOMP;
        else
            return eWAIT;  //
    } else if (mst_comm_status_->comm_status == ECommStatus::eCOMM_ERROR)
        return eERROR;
    else
        return eWAIT;
}

/**
 * @brief 放气后复测
 * @return
 */
EExecStatus CLenAdjustOpt::retest2Step(void) {
    //    static uint16_t delay_cnt = 100;
    //    while(delay_cnt--) {
    //        return eWAIT;
    //    }
    //    delay_cnt = 100;

    m_serial_thread_->task_id_change(1);
    return eOK;
}


/**
 * @brief 放气后复测
 * @return
 */
EExecStatus CLenAdjustOpt::retest2Ack(void) {
    static uint8_t facula_test2_num         = 0;
    QByteArray     origin_bytes_tmp         = m_origin_bytes;
    mst_result_->finalResult.mp_origin_data = m_origin_bytes;
    QVector<uint32_t>              target_map;
    IFaculaAdjust::UFaculaJudgeDds facula_dds_info;

    if (mc_facula_context_->faculaTest(m_origin_bytes, facula_dds_info.dds_info, target_map, IFaculaAdjust::eFACUAL_DEFLATE_TEST, true)) {
        //        emit dataAckSignal();
        mapDataShow();

        mst_result_->finalResult.result &= facula_dds_info.dds_info;  //
        if (mst_result_->finalResult.result != 0) {
            mst_result_->finalResult.mp_origin_data = origin_bytes_tmp;
        }

        QStringList mapStringList;
        for (uint32_t value : target_map) {
            mapStringList.append(QString::number(value));
        }
        LOG_INFO(MyLogger::LogType::RESULT_CACHE_DATA, QString("Deflate target 3x3 map: [%1]").arg(mapStringList.join(", ")));

        facula_test2_num++;
        if (facula_test2_num >= mst_iniConfig.facula_ok_times) {
            facula_test2_num = 0;
            m_serial_thread_->task_id_change(4);

            mst_result_->finalResult.map_matrix = mc_facula_context_->getMap();  // save last matrix
            mst_result_->target_map             = target_map;

            emit resultSignal(EResult(!mst_result_->finalResult.result), 2);  // result-0 ok

            if (mst_result_->finalResult.result == 0 || mst_iniConfig.facula_ng_handle) {
                mst_result_->finalResult.mp_origin_data = origin_bytes_tmp;
                return eCOMP;
            } else
                return eWAIT;
        }
    }

    return eWAIT;
}

/**
 * @brief CLenAdjustOpt::compStep
 */
EExecStatus CLenAdjustOpt::compStep(void) {
    QByteArray array;
    m_serial_thread_->task_id_change(2);
    if (mi_clen_machine_->stop(array))
        return eOK;
    else
        return eFATAL;
    //  else return eOK;
}

/**
 * @brief 结束
 * @return
 */
EExecStatus CLenAdjustOpt::compAck(void) {
    QString facula;
    bool    result = false;
    //* interface update
    //  dataShow();

    //* close
    m_serial_thread_->task_id_change(0);
    mi_icomm_->closePort();      // port close
    mi_icomm_dev_->closePort();  //

    //    emit subThreadSignal(false); //子线程退出
    emit compAckSignal(true);

    if ((!mst_config_->mode) && (mst_dds_->process_step_dds == 0) && (!mst_config_->is_button_close) &&
        ((mst_dds_->process_item_dds.errors == 0) ||
         ((mst_dds_->process_item_dds.all_error.xy_limit == 1 || mst_dds_->process_item_dds.all_error.z_limit == 1) &&
          (mst_result_->finalResult.result == 0)))) {  //通信正常
        float process_time_tmp = (float)mst_task_status_->exec_time / 1000;

        //******************************************* save data *************************
        //* save local data
        mm_result_data["时间"] =
            QString(QDateTime::currentDateTime().toString("yyyy-MM-dd-hh:mm:ss"));  //时间戳，转速均值，最大值，最小值，最大值偏差，最小值偏差，结果
        mm_result_data["芯片ID"] = mst_result_->chip_id;

        mm_result_data["光斑code"] = errorInfoPack(EError_type::eFACULA_JUDGE_ERRPR, mst_result_->adjustResult.result);
        facula                     = NsTypeConvert::byteArrayToString(mst_result_->adjustResult.mp_origin_data);
        mm_result_data["光斑数据"] = facula;

        if (mst_result_->final_loc.size() == 3) {
            mm_result_data["终止坐标x"] = QString::number(mst_result_->final_loc.at(0), 'f', 3);
            mm_result_data["终止坐标y"] = QString::number(mst_result_->final_loc.at(2), 'f', 3);
            mm_result_data["终止坐标z"] = QString::number(mst_result_->final_loc.at(1), 'f', 3);
        }

        mm_result_data["固化光斑code"] = errorInfoPack(EError_type::eFACULA_JUDGE_ERRPR, mst_result_->solidResult.result);
        facula                         = NsTypeConvert::byteArrayToString(mst_result_->solidResult.mp_origin_data);
        mm_result_data["固化光斑数据"] = facula;

        if (mst_result_->finalResult.map_matrix.size() > 0) {
            QString facula_matrix = "";
            for (int for_x = 0; for_x < mst_result_->finalResult.map_matrix.size(); for_x++) {
                for (int for_y = 0; for_y < mst_result_->finalResult.map_matrix.at(0).size(); for_y++) {
                    facula_matrix += QString::number(mst_result_->finalResult.map_matrix.at(for_x).at(for_y), 10) + " ";
                }
            }
            mm_result_data["光斑MP"] = facula_matrix;
        }
        mm_result_data["放气光斑code"] = errorInfoPack(EError_type::eFACULA_JUDGE_ERRPR, mst_result_->finalResult.result);

        mm_result_data["结果"]         = QString(mst_result_->finalResult.result == 0 ? "PASS" : "NG");
        mm_result_data["fatal reason"] = mst_result_->fatal_reason + mm_result_data["放气光斑code"].toString();

        mm_result_data["运行时间"] = QString::number(process_time_tmp, 'f', 1);

        //* save mes data
        if (mst_iniConfig.userid != MES_SUPER_USER && (mst_dds_->mes_dds == 0)) {  // mes正常) { //非测试账号，连接MES
            mesDataHandle(mst_mes_xsub2_data_);
            mst_dds_->mes_dds |= mc_sql_handle_->saveMesData(*mst_xpid_det_, mst_mes_xsub2_data_);

            if (mst_dds_->mes_dds != 0) {
                QString error_info = errorInfoPack(EError_type::eMES_ERROR, mst_dds_->mes_dds);
                emit    moduleInfoShowSignal(mst_dds_->mes_dds, error_info);
            }
            LOG_INFO(MyLogger::LogType::ERROR_LOG, QString("mes save code: %1").arg(QString::number(mst_dds_->mes_dds, 16)));
        }
        mm_result_data["事务号"]    = mst_mes_xsub2_data_->trnbr;
        mm_result_data["标签号nbr"] = mst_mes_xsub2_data_->nbr;
        mm_result_data["mes code"]  = QString::number(mst_dds_->mes_dds, 16);
        mi_save_file_->writeFile(mm_result_data);


        //* save product test info
        mst_product_detect_info_->product_total_num++;
        mst_product_detect_info_->process_time = process_time_tmp;
        if (mst_result_->finalResult.result == 0) {
            mst_product_detect_info_->good_product_num++;
        } else {
            mst_product_detect_info_->bad_product_num++;
        }
        mst_product_detect_info_->bad_product_rate = (float)mst_product_detect_info_->bad_product_num / mst_product_detect_info_->product_total_num;
        mst_product_detect_info_->aver_process_time =
            (mst_product_detect_info_->aver_process_time * (mst_product_detect_info_->product_total_num - 1) + mst_product_detect_info_->process_time) /
            mst_product_detect_info_->product_total_num;
        emit productTestInfoSignal(false);

        m_product_info["product_total_num"] = QString::number(mst_product_detect_info_->product_total_num, 10);
        m_product_info["good_product_num"]  = QString::number(mst_product_detect_info_->good_product_num, 10);
        m_product_info["bad_product_num"]   = QString::number(mst_product_detect_info_->bad_product_num, 10);
        m_product_info["bad_product_rate"]  = QString::number(mst_product_detect_info_->bad_product_rate, 'f', 3);
        m_product_info["process_time"]      = QString::number(mst_product_detect_info_->process_time, 'f', 1);
        m_product_info["aver_process_time"] = QString::number(mst_product_detect_info_->aver_process_time, 'f', 1);
        QString filename                    = QApplication::applicationDirPath() + "/config/clen_product_info.xml";
        mi_load_->writeParam(filename, "clens_info", "product_detect_info", &m_product_info);
    } else {
        result = false;
    }
    mst_result_->final_result = result;
    //    mc_processList_->taskInit(&mv_task_list, mst_task_status_);

    return eCOMP;
}
