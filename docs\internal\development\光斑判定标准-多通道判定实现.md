# 光斑判定标准-多通道判定实现

[[光斑判定标准-多通道判定.excalidraw]]
[[光斑判定标准-多通道判定.md]]


[[clen_config.ini]]

facula_center_loc_x=2 ;;光斑中心坐标x
facula_center_loc_y=2 ;;光斑中心坐标y

方案：

当前光路功能主要分为三步：

1. 调节 + 判定
2. 固化后判定，
3. 放气后判定，

修改facula_center_loc_x，facula_center_loc_y 参数名称，并改为可以设置多通道。

- 没有配置，默认是中心通道
- 配置一个通道，三个判定，光斑中心(最强值)只能是在这个通道
- 配置多个通道，三个判定，判定光斑中心处于这些通道中且peak值大于配置值，跳过后续其他的判定(对称，周围通道判定等等)

修改部分：

- 配置文件
- CFaculaCircle::faculaTest    

if (mu_facula_detect_items.all_info.facula_target) {  // 光斑中心
        if (!((target_x_delta == 0) && (target_y_delta == 0))) {
            facula_dds_info.all_info.facula_target = true;
        }
    }

文档更新：

[[TOF接收镜片光路耦合软件使用文档]]