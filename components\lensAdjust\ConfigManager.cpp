#include "ConfigManager.h"
#include <QApplication>
#include <QDebug>
#include <QDir>
#include <QFile>
#include <QMutexLocker>
#include <QStandardPaths>
#include <QTextStream>
#include <QXmlStreamReader>
#include <QXmlStreamWriter>

ConfigManager *ConfigManager::s_instance = nullptr;
QMutex         ConfigManager::s_mutex;

ConfigManager *ConfigManager::getInstance() {
    QMutexLocker locker(&s_mutex);
    if (!s_instance) {
        s_instance = new ConfigManager();
    }
    return s_instance;
}

ConfigManager::ConfigManager(QObject *parent) : QObject(parent), m_fileWatcher(new QFileSystemWatcher(this)) {
    // 初始化默认值
    memset(&m_systemConfig, 0, sizeof(m_systemConfig));
    memset(&m_faculaConfig, 0, sizeof(m_faculaConfig));
    memset(&m_hardwareConfig, 0, sizeof(m_hardwareConfig));

    // 设置文件监控
    setupFileWatcher();

    // 连接信号
    connect(m_fileWatcher, &QFileSystemWatcher::fileChanged, this, &ConfigManager::onConfigFileChanged);

    logInfo("ConfigManager initialized");
}

ConfigManager::~ConfigManager() {
    saveAllConfigs();
}

bool ConfigManager::loadAllConfigs() {
    logInfo("Loading all configuration files...");

    bool success = true;

    // 检查并生成缺失的配置文件
    if (!generateDefaultConfigs()) {
        logError("Failed to generate default configurations");
        return false;
    }

    // 加载各个配置文件
    success &= loadConfig(SystemConfig);
    success &= loadConfig(FaculaConfig);
    success &= loadConfig(HardwareConfig);
    success &= loadConfig(AlgorithmConfig);

    if (success) {
        logInfo("All configuration files loaded successfully");
    } else {
        logError("Some configuration files failed to load");
    }

    return success;
}

bool ConfigManager::saveAllConfigs() {
    logInfo("Saving all configuration files...");

    bool success = true;
    success &= saveConfig(SystemConfig);
    success &= saveConfig(FaculaConfig);
    success &= saveConfig(HardwareConfig);
    success &= saveConfig(AlgorithmConfig);

    if (success) {
        logInfo("All configuration files saved successfully");
    } else {
        logError("Some configuration files failed to save");
    }

    return success;
}

bool ConfigManager::loadConfig(ConfigType type) {
    switch (type) {
    case SystemConfig:
        return loadSystemConfig();
    case FaculaConfig:
        return loadFaculaConfig();
    case HardwareConfig:
        return loadHardwareConfig();
    case AlgorithmConfig:
        return loadAlgorithmConfig();
    default:
        logError(QString("Unknown config type: %1").arg(type));
        return false;
    }
}

bool ConfigManager::saveConfig(ConfigType type) {
    switch (type) {
    case SystemConfig:
        return saveSystemConfig();
    case FaculaConfig:
        return saveFaculaConfig();
    case HardwareConfig:
        return saveHardwareConfig();
    case AlgorithmConfig:
        return saveAlgorithmConfig();
    default:
        logError(QString("Unknown config type: %1").arg(type));
        return false;
    }
}

QString ConfigManager::getConfigFilePath(ConfigType type) const {
    QString configDir = QApplication::applicationDirPath() + "/config/";

    switch (type) {
    case SystemConfig:
        return configDir + "system_config.ini";
    case FaculaConfig:
        return configDir + "facula_config.ini";
    case HardwareConfig:
        return configDir + "hardware_config.ini";
    case AlgorithmConfig:
        return configDir + "algorithm_config.xml";
    default:
        return QString();
    }
}

bool ConfigManager::configFileExists(ConfigType type) const {
    return QFile::exists(getConfigFilePath(type));
}

bool ConfigManager::generateDefaultConfigs() {
    logInfo("Checking and generating default configuration files...");

    // 确保配置目录存在
    QString configDir = QApplication::applicationDirPath() + "/config/";
    QDir    dir;
    if (!dir.exists(configDir)) {
        if (!dir.mkpath(configDir)) {
            logError("Failed to create config directory: " + configDir);
            return false;
        }
    }

    bool success = true;

    // 生成缺失的配置文件
    if (!configFileExists(SystemConfig)) {
        success &= generateDefaultSystemConfig();
    }

    if (!configFileExists(FaculaConfig)) {
        success &= generateDefaultFaculaConfig();
    }

    if (!configFileExists(HardwareConfig)) {
        success &= generateDefaultHardwareConfig();
    }

    if (!configFileExists(AlgorithmConfig)) {
        success &= generateDefaultAlgorithmConfig();
    }

    return success;
}

void ConfigManager::setupFileWatcher() {
    // 添加配置文件到监控列表
    QStringList configFiles;
    configFiles << getConfigFilePath(SystemConfig) << getConfigFilePath(FaculaConfig) << getConfigFilePath(HardwareConfig)
                << getConfigFilePath(AlgorithmConfig);

    for (const QString &file : configFiles) {
        if (QFile::exists(file)) {
            m_fileWatcher->addPath(file);
        }
    }
}

void ConfigManager::onConfigFileChanged(const QString &path) {
    logInfo("Configuration file changed: " + path);

    // 确定配置文件类型并重新加载
    if (path == getConfigFilePath(SystemConfig)) {
        loadConfig(SystemConfig);
        emit configChanged(SystemConfig);
    } else if (path == getConfigFilePath(FaculaConfig)) {
        loadConfig(FaculaConfig);
        emit configChanged(FaculaConfig);
    } else if (path == getConfigFilePath(HardwareConfig)) {
        loadConfig(HardwareConfig);
        emit configChanged(HardwareConfig);
    } else if (path == getConfigFilePath(AlgorithmConfig)) {
        loadConfig(AlgorithmConfig);
        emit configChanged(AlgorithmConfig);
    }
}

void ConfigManager::logError(const QString &message) {
    qCritical() << "[ConfigManager]" << message;
    emit configError(message);
}

void ConfigManager::logInfo(const QString &message) {
    qDebug() << "[ConfigManager]" << message;
}

// 系统配置加载
bool ConfigManager::loadSystemConfig() {
    QString filePath = getConfigFilePath(SystemConfig);
    if (!QFile::exists(filePath)) {
        logError("System config file not found: " + filePath);
        return false;
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取系统配置
    m_systemConfig.version     = settings.value("SENSOR_BOARD/version", "1.0.0").toString();
    m_systemConfig.userid      = settings.value("MES/userid", "admin").toString();
    m_systemConfig.op          = settings.value("MES/op", "105").toString();
    m_systemConfig.work_number = settings.value("MES/work_number", "10001").toString();
    m_systemConfig.work_domain = settings.value("MES/work_domain", "001").toString();

    m_systemConfig.sensor_device       = settings.value("DEVICE/sensor_device", 4).toUInt();
    m_systemConfig.sensor_device_baud  = settings.value("DEVICE/sensor_device_baud", 230400).toUInt();
    m_systemConfig.station_number      = settings.value("DEVICE/station_number", 1).toUInt();
    m_systemConfig.clens_machine_brand = settings.value("DEVICE/clens_machine_brand", 3).toUInt();

    logInfo("System configuration loaded successfully");
    return true;
}

// 光斑配置加载
bool ConfigManager::loadFaculaConfig() {
    QString filePath = getConfigFilePath(FaculaConfig);
    if (!QFile::exists(filePath)) {
        logError("Facula config file not found: " + filePath);
        return false;
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取多通道配置
    m_faculaConfig.facula_center_channels       = settings.value("FACULA_CENTER/facula_center_channels").toString();
    m_faculaConfig.facula_center_peak_threshold = settings.value("FACULA_CENTER/facula_center_peak_threshold", 800).toUInt();

    // 解析多通道配置
    if (!m_faculaConfig.facula_center_channels.isEmpty()) {
        m_faculaConfig.facula_center_points = parseFaculaCenterChannels(m_faculaConfig.facula_center_channels);
    }

    // 兼容性处理：如果没有多通道配置，使用原有单点配置
    if (m_faculaConfig.facula_center_points.isEmpty()) {
        uint8_t x = settings.value("FACULA_CENTER/facula_center_loc_x", 2).toUInt();
        uint8_t y = settings.value("FACULA_CENTER/facula_center_loc_y", 2).toUInt();
        m_faculaConfig.facula_center_points.append(QPoint(x, y));
        m_faculaConfig.facula_center_channels = QString("%1,%2").arg(x).arg(y);
    }

    // 设置兼容性字段
    if (!m_faculaConfig.facula_center_points.isEmpty()) {
        m_faculaConfig.facula_center_loc_x = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().x());
        m_faculaConfig.facula_center_loc_y = static_cast<uint8_t>(m_faculaConfig.facula_center_points.first().y());
    } else {
        m_faculaConfig.facula_center_loc_x = 2;
        m_faculaConfig.facula_center_loc_y = 2;
    }

    // 读取其他光斑参数
    m_faculaConfig.facula_ok_times    = settings.value("ADJUST_PARAM/facula_ok_times", 3).toUInt();
    m_faculaConfig.solid_time         = settings.value("ADJUST_PARAM/solid_time", 0).toUInt();
    m_faculaConfig.facula_ng_handle   = settings.value("ADJUST_PARAM/facula_ng_handle", 1).toUInt();
    m_faculaConfig.facula_handle_type = settings.value("FACULA_HANDLE/facula_handle_type", 1).toUInt();

    // 读取图像处理参数
    m_faculaConfig.interpolation_type      = settings.value("FACULA_PROCESSING/interpolation_type", 0).toUInt();
    m_faculaConfig.filter_types            = settings.value("FACULA_PROCESSING/filter_types", "6").toString();
    m_faculaConfig.interpolation_offset    = settings.value("FACULA_PROCESSING/interpolation_offset", 0.5).toFloat();
    m_faculaConfig.kalman_strength         = settings.value("FACULA_PROCESSING/kalman_strength", 1.0).toFloat();
    m_faculaConfig.convolution_kernel_size = settings.value("FACULA_PROCESSING/convolution_kernel_size", 3).toUInt();
    m_faculaConfig.convolution_preset      = settings.value("FACULA_PROCESSING/convolution_preset", "sharpen").toString();

    logInfo("Facula configuration loaded successfully");
    return true;
}

// 硬件配置加载
bool ConfigManager::loadHardwareConfig() {
    QString filePath = getConfigFilePath(HardwareConfig);
    if (!QFile::exists(filePath)) {
        logError("Hardware config file not found: " + filePath);
        return false;
    }

    QSettings settings(filePath, QSettings::IniFormat);

    // 读取硬件配置
    m_hardwareConfig.xy_radius_limit = settings.value("HARDWARE/xy_radius_limit", 1500).toUInt();
    m_hardwareConfig.z_radius_limit  = settings.value("HARDWARE/z_radius_limit", 1400).toUInt();
    m_hardwareConfig.x_step_dist     = settings.value("HARDWARE/x_step_dist", 10).toUInt();
    m_hardwareConfig.y_step_dist     = settings.value("HARDWARE/y_step_dist", 10).toUInt();
    m_hardwareConfig.z_step_dist     = settings.value("HARDWARE/z_step_dist", 10).toUInt();

    logInfo("Hardware configuration loaded successfully");
    return true;
}

// 算法配置加载
bool ConfigManager::loadAlgorithmConfig() {
    QString filePath = getConfigFilePath(AlgorithmConfig);
    if (!QFile::exists(filePath)) {
        logError("Algorithm config file not found: " + filePath);
        return false;
    }

    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly | QIODevice::Text)) {
        logError("Cannot open algorithm config file: " + filePath);
        return false;
    }

    QXmlStreamReader xmlReader(&file);
    m_algorithmConfig.parameters.clear();

    while (!xmlReader.atEnd() && !xmlReader.hasError()) {
        QXmlStreamReader::TokenType token = xmlReader.readNext();

        if (token == QXmlStreamReader::StartElement) {
            if (xmlReader.name() != "Parameters") {
                QString elementName  = xmlReader.name().toString();
                QString elementValue = xmlReader.readElementText();

                bool ok;
                int  value = elementValue.toInt(&ok);
                if (ok) {
                    m_algorithmConfig.parameters[elementName] = value;
                }
            }
        }
    }

    file.close();

    if (xmlReader.hasError()) {
        logError("XML parsing error in algorithm config: " + xmlReader.errorString());
        return false;
    }

    logInfo("Algorithm configuration loaded successfully");
    return true;
}

// 多通道配置解析方法
QVector<QPoint> ConfigManager::parseFaculaCenterChannels(const QString &channelsStr) {
    QVector<QPoint> points;

    if (channelsStr.isEmpty()) {
        return points;
    }

    // 按分号分割各个通道
    QStringList channels = channelsStr.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channels) {
        // 按逗号分割x,y坐标
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);

        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk) {
                QPoint point(x, y);
                if (isValidChannelPoint(point)) {
                    points.append(point);
                } else {
                    logError(QString("Invalid channel point: (%1,%2) - coordinates out of range").arg(x).arg(y));
                }
            } else {
                logError(QString("Invalid channel format: %1 - coordinates must be integers").arg(channel));
            }
        } else {
            logError(QString("Invalid channel format: %1 - expected format: x,y").arg(channel));
        }
    }

    if (points.isEmpty()) {
        logError("No valid channels found in configuration: " + channelsStr);
    } else {
        logInfo(QString("Parsed %1 facula center channels").arg(points.size()));
    }

    return points;
}

bool ConfigManager::isValidChannelPoint(const QPoint &point, uint8_t maxX, uint8_t maxY) {
    return (point.x() >= 0 && point.x() <= maxX && point.y() >= 0 && point.y() <= maxY);
}
