#include "lensReadIni.h"


ClensReadIni *ClensReadIni::instance = NULL;


ClensReadIni *ClensReadIni::getInstance() {
    if (instance == NULL) {
        instance = new ClensReadIni();
    }
    return instance;
}


ClensReadIni::ClensReadIni() {
    this->readIni();
}


void ClensReadIni::readIni() {
    QSettings *configIniRead_ = new QSettings("config/clen_config.ini", QSettings::IniFormat);  //初始化读取Ini文件对象

    //    configIniRead_->beginGroup("SENSOR_BOARD");
    //    iniConfig.version = configIniRead_->value("version").toString();
    iniConfig.version             = configIniRead_->value("SENSOR_BOARD/version").toString();
    iniConfig.userid              = "U" + configIniRead_->value("MES/userid").toString();
    iniConfig.op                  = configIniRead_->value("MES/op").toString();
    iniConfig.work_number         = configIniRead_->value("MES/work_number").toString();
    iniConfig.work_domain         = configIniRead_->value("MES/work_domain").toString();
    iniConfig.sensor_device       = configIniRead_->value("DEVICE/sensor_device").toInt();
    iniConfig.sensor_device_buad  = configIniRead_->value("DEVICE/sensor_device_baud").toUInt();
    iniConfig.station_number      = configIniRead_->value("DEVICE/station_number").toUInt();
    iniConfig.clens_machine_brand = configIniRead_->value("DEVICE/clens_machine_brand").toUInt();
    // 读取多通道配置
    iniConfig.facula_center_channels       = configIniRead_->value("ADJUST_PARAM/facula_center_channels").toString();
    iniConfig.facula_center_peak_threshold = configIniRead_->value("ADJUST_PARAM/facula_center_peak_threshold", 800).toUInt();

    // 解析多通道配置
    if (!iniConfig.facula_center_channels.isEmpty()) {
        iniConfig.facula_center_points = parseFaculaCenterChannels(iniConfig.facula_center_channels);
    }

    // 兼容性处理：如果没有多通道配置，使用原有单点配置
    if (iniConfig.facula_center_points.isEmpty()) {
        uint8_t x = configIniRead_->value("ADJUST_PARAM/facula_center_loc_x", 2).toUInt();
        uint8_t y = configIniRead_->value("ADJUST_PARAM/facula_center_loc_y", 2).toUInt();
        iniConfig.facula_center_points.append(QPoint(x, y));
        iniConfig.facula_center_channels = QString("%1,%2").arg(x).arg(y);
    }

    // 设置兼容性字段（取第一个通道点）
    if (!iniConfig.facula_center_points.isEmpty()) {
        iniConfig.facula_center_loc_x = static_cast<uint8_t>(iniConfig.facula_center_points.first().x());
        iniConfig.facula_center_loc_y = static_cast<uint8_t>(iniConfig.facula_center_points.first().y());
    } else {
        iniConfig.facula_center_loc_x = 2;  // 默认值
        iniConfig.facula_center_loc_y = 2;  // 默认值
    }

    iniConfig.facula_ok_times  = configIniRead_->value("ADJUST_PARAM/facula_ok_time").toUInt();
    iniConfig.solid_time       = configIniRead_->value("ADJUST_PARAM/solid_time").toUInt();
    iniConfig.facula_ng_handle = configIniRead_->value("ADJUST_PARAM/facula_ng_handle").toUInt();

    // 读取光斑处理配置，如果不存在则使用默认值
    // iniConfig.interpolation_type      = configIniRead_->value("FACULA_PROCESSING/interpolation_type", 0).toUInt();
    // iniConfig.filter_types            = configIniRead_->value("FACULA_PROCESSING/filter_types", "2").toString();
    // iniConfig.interpolation_offset    = configIniRead_->value("FACULA_PROCESSING/interpolation_offset", 0.5).toFloat();
    // iniConfig.kalman_strength         = configIniRead_->value("FACULA_PROCESSING/kalman_strength", 1.0).toFloat();
    // iniConfig.convolution_kernel_size = configIniRead_->value("FACULA_PROCESSING/convolution_kernel_size", 3).toUInt();

    iniConfig.facula_handle_type = configIniRead_->value("FACULA_HANDLE/facula_handle_type").toUInt();

    delete configIniRead_;
}


const ClensReadIni::IniConfig &ClensReadIni::getIniConfig() {
    return iniConfig;
}

/**
 * @brief 解析多通道光斑中心配置字符串
 * @param channelsStr 配置字符串，格式："x1,y1;x2,y2;x3,y3"
 * @return 解析后的通道坐标列表
 */
QVector<QPoint> ClensReadIni::parseFaculaCenterChannels(const QString &channelsStr) {
    QVector<QPoint> points;

    if (channelsStr.isEmpty()) {
        return points;
    }

    // 按分号分割各个通道
    QStringList channels = channelsStr.split(';', Qt::SkipEmptyParts);

    for (const QString &channel : channels) {
        // 按逗号分割x,y坐标
        QStringList coords = channel.split(',', Qt::SkipEmptyParts);

        if (coords.size() == 2) {
            bool xOk, yOk;
            int  x = coords[0].trimmed().toInt(&xOk);
            int  y = coords[1].trimmed().toInt(&yOk);

            if (xOk && yOk) {
                QPoint point(x, y);
                if (isValidChannelPoint(point)) {
                    points.append(point);
                } else {
                    qWarning() << "Invalid channel point:" << point << "- coordinates out of range";
                }
            } else {
                qWarning() << "Invalid channel format:" << channel << "- coordinates must be integers";
            }
        } else {
            qWarning() << "Invalid channel format:" << channel << "- expected format: x,y";
        }
    }

    if (points.isEmpty()) {
        qWarning() << "No valid channels found in configuration:" << channelsStr;
    } else {
        qDebug() << "Parsed" << points.size() << "facula center channels:" << points;
    }

    return points;
}

/**
 * @brief 验证通道坐标是否有效
 * @param point 通道坐标点
 * @param maxX 最大X坐标值
 * @param maxY 最大Y坐标值
 * @return 坐标是否有效
 */
bool ClensReadIni::isValidChannelPoint(const QPoint &point, uint8_t maxX, uint8_t maxY) {
    return (point.x() >= 0 && point.x() <= maxX && point.y() >= 0 && point.y() <= maxY);
}
